# Docker构建忽略文件
# 优化构建速度和镜像大小

# Git相关
.git
.gitignore
.gitattributes
.gitmodules

# Docker相关
Dockerfile*
docker-compose*.yml
.dockerignore

# 数据目录 (避免将大文件打包到镜像中)
data/
workspace/
*.log
*.logs

# 临时文件
*.tmp
*.temp
.tmp/
.temp/

# 系统文件
.DS_Store
Thumbs.db
*.swp
*.swo
*~

# IDE配置
.vscode/
.idea/
*.sublime-*
.atom/

# Python相关
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Node.js相关
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn-integrity

# Rust相关
target/
Cargo.lock

# Go相关
vendor/
*.exe
*.exe~
*.dll
*.so
*.dylib
*.test
*.out

# C++相关
*.o
*.obj
*.exe
*.dll
*.so
*.dylib
*.a
*.lib
*.out
build/
cmake-build-*/

# 日志文件
*.log
logs/

# 缓存目录
.cache/
cache/

# 备份文件
*.bak
*.backup
*.old

# 压缩文件
*.zip
*.tar.gz
*.rar
*.7z

# 文档
README.md
DOCKER_USAGE.md
*.md

# 脚本文件
*.sh
setup-environment.sh

# 配置文件
.env*
config.json
settings.json
