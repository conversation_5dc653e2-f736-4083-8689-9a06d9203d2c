# =============================================================================
# 凤凰涅槃计划V3：终极AI开发环境 - 多阶段构建版本
# 支持CUDA GPU计算、Go微服务、C++高性能、Python AI/ML
#
# 构建特性：
# - 多阶段构建，支持增量构建和错误恢复
# - 网络容错机制，适配中国大陆环境
# - 智能依赖冲突处理
# - 可选组件安装，提高构建成功率
# =============================================================================

# -----------------------------------------------------------------------------
# 阶段1：基础系统环境准备
# 功能：配置镜像源、安装基础工具、设置环境变量
# -----------------------------------------------------------------------------
FROM nvidia/cuda:12.1.1-cudnn8-devel-ubuntu22.04 AS base-system

# 全局环境变量设置
ENV DEBIAN_FRONTEND=noninteractive \
    PYTHONUNBUFFERED=1 \
    CUDA_HOME=/usr/local/cuda \
    # 网络超时设置 (中国大陆优化)
    APT_TIMEOUT=300 \
    WGET_TIMEOUT=60 \
    CURL_TIMEOUT=60 \
    # 重试次数设置
    MAX_RETRIES=3

# 创建错误处理函数和工具脚本
RUN echo '#!/bin/bash' > /usr/local/bin/retry_cmd && \
    echo '# 通用重试命令函数' >> /usr/local/bin/retry_cmd && \
    echo 'MAX_ATTEMPTS=${MAX_RETRIES:-3}' >> /usr/local/bin/retry_cmd && \
    echo 'DELAY=5' >> /usr/local/bin/retry_cmd && \
    echo 'for i in $(seq 1 $MAX_ATTEMPTS); do' >> /usr/local/bin/retry_cmd && \
    echo '  echo "🔄 尝试执行命令 (第 $i/$MAX_ATTEMPTS 次): $*"' >> /usr/local/bin/retry_cmd && \
    echo '  if "$@"; then' >> /usr/local/bin/retry_cmd && \
    echo '    echo "✅ 命令执行成功"' >> /usr/local/bin/retry_cmd && \
    echo '    exit 0' >> /usr/local/bin/retry_cmd && \
    echo '  else' >> /usr/local/bin/retry_cmd && \
    echo '    echo "❌ 命令执行失败 (第 $i 次)"' >> /usr/local/bin/retry_cmd && \
    echo '    if [ $i -lt $MAX_ATTEMPTS ]; then' >> /usr/local/bin/retry_cmd && \
    echo '      echo "⏳ 等待 $DELAY 秒后重试..."' >> /usr/local/bin/retry_cmd && \
    echo '      sleep $DELAY' >> /usr/local/bin/retry_cmd && \
    echo '      DELAY=$((DELAY * 2))' >> /usr/local/bin/retry_cmd && \
    echo '    fi' >> /usr/local/bin/retry_cmd && \
    echo '  fi' >> /usr/local/bin/retry_cmd && \
    echo 'done' >> /usr/local/bin/retry_cmd && \
    echo 'echo "💥 所有重试均失败，命令: $*"' >> /usr/local/bin/retry_cmd && \
    echo 'exit 1' >> /usr/local/bin/retry_cmd && \
    chmod +x /usr/local/bin/retry_cmd

# 配置中国大陆镜像源 (带容错机制)
RUN echo "🌏 配置中国大陆镜像源..." && \
    # 备份原始源
    cp /etc/apt/sources.list /etc/apt/sources.list.backup && \
    # 尝试配置阿里云镜像源
    (sed -i 's@//.*archive.ubuntu.com@//mirrors.aliyun.com@g' /etc/apt/sources.list && \
     sed -i 's@//.*security.ubuntu.com@//mirrors.aliyun.com@g' /etc/apt/sources.list && \
     echo "✅ 阿里云镜像源配置成功") || \
    # 如果失败，尝试清华镜像源
    (echo "⚠️  阿里云镜像源配置失败，尝试清华镜像源..." && \
     cp /etc/apt/sources.list.backup /etc/apt/sources.list && \
     sed -i 's@//.*archive.ubuntu.com@//mirrors.tuna.tsinghua.edu.cn@g' /etc/apt/sources.list && \
     sed -i 's@//.*security.ubuntu.com@//mirrors.tuna.tsinghua.edu.cn@g' /etc/apt/sources.list && \
     echo "✅ 清华镜像源配置成功") || \
    # 最后回退到原始源
    (echo "⚠️  所有镜像源配置失败，使用原始源..." && \
     cp /etc/apt/sources.list.backup /etc/apt/sources.list)

# 检查是否在中国大陆环境
RUN echo "🌏 检查构建环境..." && \
    apt-get update && apt-get install -y curl && \
    (curl -s --connect-timeout 5 https://mirrors.tuna.tsinghua.edu.cn > /dev/null || \
     (echo "❌ 错误: 此Dockerfile只能在中国大陆环境下构建!" && \
      echo "请确保网络可以访问中国大陆的镜像源。" && \
      exit 1)) && \
    echo "✅ 中国大陆环境检查通过"

# 安装系统依赖
RUN apt-get update && apt-get install -y \
        # 基础工具
        wget curl git vim nano htop tree unzip \
        build-essential cmake pkg-config \
        # Python开发依赖
        python3-dev python3-pip \
        libssl-dev libffi-dev \
        # 图形和多媒体库
        libgl1-mesa-glx libglib2.0-0 libsm6 libxext6 \
        libxrender-dev libgomp1 \
        # 网络和安全工具
        openssh-client rsync \
        # 现代化CLI工具
        zsh ripgrep fd-find bat exa \
        # C/C++高性能开发工具
        clang llvm gdb valgrind \
        clang-format clang-tidy \
        libbenchmark-dev libgtest-dev \
        # Go微服务开发依赖
        protobuf-compiler \
        # 构建工具
        pkg-config autoconf automake libtool ninja-build \
        # TensorRT和ONNX依赖
        libnvinfer-dev libnvonnxparsers-dev \
    && update-ca-certificates \
    && apt-get autoremove -y \
    && apt-get autoclean \
    && rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

# 安装CUDA开发工具 (分步安装，避免依赖冲突)
RUN apt-get update && \
    # 安装核心CUDA开发工具
    apt-get install -y --no-install-recommends \
        # CUDA编译器和核心工具
        cuda-nvcc-12-1 \
        cuda-nvtx-12-1 \
        cuda-gdb-12-1 \
        # CUDA数学库开发包 (与CUDA 12.1兼容)
        libcublas-dev-12-1 \
        libcurand-dev-12-1 \
        libcufft-dev-12-1 \
        libcusparse-dev-12-1 \
        libcusolver-dev-12-1 \
        # 性能分析工具
        cuda-profiler-api-12-1 \
    && apt-get autoremove -y \
    && apt-get autoclean \
    && rm -rf /var/lib/apt/lists/*

# 处理cuDNN开发包 (使用基础镜像兼容版本)
RUN apt-get update && \
    # 获取当前已安装的cuDNN版本
    CUDNN_VERSION=$(dpkg -l | grep libcudnn8 | awk '{print $3}' | head -1) && \
    echo "检测到cuDNN版本: $CUDNN_VERSION" && \
    # 安装对应版本的开发包，如果失败则跳过
    (apt-get install -y --no-install-recommends libcudnn8-dev=$CUDNN_VERSION || \
     echo "⚠️  cuDNN开发包安装失败，将使用运行时版本") && \
    apt-get autoremove -y && \
    apt-get autoclean && \
    rm -rf /var/lib/apt/lists/*

# 创建cuDNN开发环境链接 (如果开发包安装失败)
RUN if [ ! -f /usr/include/cudnn.h ]; then \
        echo "🔧 创建cuDNN开发环境..." && \
        # 查找cuDNN头文件
        find /usr -name "cudnn*.h" 2>/dev/null | head -1 | xargs -I {} ln -sf {} /usr/include/cudnn.h || \
        echo "⚠️  未找到cuDNN头文件，某些编译功能可能受限"; \
    fi

# 安装TensorRT (优先使用apt，失败则通过pip)
RUN apt-get update && \
    # 尝试安装TensorRT运行时
    (apt-get install -y --no-install-recommends \
        libnvinfer8 \
        libnvinfer-plugin8 \
        libnvonnxparsers8 \
        libnvparsers8 || \
     echo "⚠️  TensorRT apt安装失败，将通过pip安装") && \
    apt-get autoremove -y && \
    apt-get autoclean && \
    rm -rf /var/lib/apt/lists/*

# 安装Go语言
RUN wget -O /tmp/go.tar.gz https://mirrors.aliyun.com/golang/go1.21.5.linux-amd64.tar.gz && \
    tar -C /usr/local -xzf /tmp/go.tar.gz && \
    rm /tmp/go.tar.gz && \
    # 创建Go工作目录
    mkdir -p /workspace/go/{bin,src,pkg} && \
    # 安装Go工具
    go install golang.org/x/tools/gopls@latest && \
    go install github.com/go-delve/delve/cmd/dlv@latest && \
    go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest && \
    go install google.golang.org/protobuf/cmd/protoc-gen-go@latest && \
    go install google.golang.org/grpc/cmd/protoc-gen-go-grpc@latest

# 安装Rust工具链
RUN curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y && \
    source /root/.cargo/env && \
    rustup component add rust-analyzer && \
    rustup component add clippy && \
    rustup component add rustfmt && \
    cargo install cargo-watch

# 安装Miniconda
RUN curl -fsSL -o /tmp/miniconda.sh \
        https://mirrors.tuna.tsinghua.edu.cn/anaconda/miniconda/Miniconda3-latest-Linux-x86_64.sh && \
    bash /tmp/miniconda.sh -b -p /opt/miniconda && \
    rm /tmp/miniconda.sh && \
    /opt/miniconda/bin/conda clean -afy

# 创建Conda环境
RUN conda create -n llm_dev python=3.10 -y --override-channels \
        -c https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/ \
        -c https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/free/ \
        -c https://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud/conda-forge/ && \
    conda clean -afy

# 激活环境并设置为默认
RUN echo "conda activate llm_dev" >> /root/.bashrc && \
    echo "conda activate llm_dev" >> /root/.zshrc

# 安装Oh My Zsh
WORKDIR /root
RUN sh -c "$(curl -fsSL https://raw.githubusercontent.com/ohmyzsh/ohmyzsh/master/tools/install.sh)" "" --unattended && \
    git clone --depth=1 https://github.com/zsh-users/zsh-autosuggestions.git \
        ${ZSH_CUSTOM:-~/.oh-my-zsh/custom}/plugins/zsh-autosuggestions && \
    git clone --depth=1 https://github.com/zsh-users/zsh-syntax-highlighting.git \
        ${ZSH_CUSTOM:-~/.oh-my-zsh/custom}/plugins/zsh-syntax-highlighting

# 安装Starship提示符
RUN curl -fsSL https://starship.rs/install.sh | sh -s -- --yes

# 配置.zshrc
RUN sed -i 's/ZSH_THEME="robbyrussell"/ZSH_THEME=""/g' ~/.zshrc && \
    echo '' >> ~/.zshrc && \
    echo '# 插件配置' >> ~/.zshrc && \
    echo 'plugins=(git conda-env docker python pip golang rust zsh-autosuggestions zsh-syntax-highlighting)' >> ~/.zshrc && \
    echo '' >> ~/.zshrc && \
    echo '# Starship提示符' >> ~/.zshrc && \
    echo 'eval "$(starship init zsh)"' >> ~/.zshrc && \
    echo '' >> ~/.zshrc && \
    echo '# 环境变量' >> ~/.zshrc && \
    echo 'export EDITOR=vim' >> ~/.zshrc && \
    echo 'export PAGER=less' >> ~/.zshrc && \
    echo 'export HISTSIZE=10000' >> ~/.zshrc && \
    echo 'export SAVEHIST=10000' >> ~/.zshrc && \
    echo 'export GOPATH=/workspace/go' >> ~/.zshrc && \
    echo 'export GOROOT=/usr/local/go' >> ~/.zshrc && \
    echo 'export RUSTUP_HOME=/root/.rustup' >> ~/.zshrc && \
    echo 'export CARGO_HOME=/root/.cargo' >> ~/.zshrc && \
    echo '' >> ~/.zshrc && \
    echo '# 多语言开发别名' >> ~/.zshrc && \
    echo 'alias gpu-status="nvidia-smi"' >> ~/.zshrc && \
    echo 'alias gpu-watch="watch -n 1 nvidia-smi"' >> ~/.zshrc && \
    echo 'alias py="python"' >> ~/.zshrc && \
    echo 'alias rs="rustc"' >> ~/.zshrc && \
    echo 'alias cc="clang++"' >> ~/.zshrc && \
    echo 'alias nv="nvcc"' >> ~/.zshrc

# 创建Starship配置
RUN mkdir -p ~/.config && \
    echo '[format]' > ~/.config/starship.toml && \
    echo '$all$character' >> ~/.config/starship.toml && \
    echo '' >> ~/.config/starship.toml && \
    echo '[character]' >> ~/.config/starship.toml && \
    echo 'success_symbol = "[➜](bold green)"' >> ~/.config/starship.toml && \
    echo 'error_symbol = "[➜](bold red)"' >> ~/.config/starship.toml && \
    echo '' >> ~/.config/starship.toml && \
    echo '[conda]' >> ~/.config/starship.toml && \
    echo 'format = "[$symbol$environment]($style) "' >> ~/.config/starship.toml && \
    echo 'symbol = "🐍 "' >> ~/.config/starship.toml

# 创建vLLM配置目录
RUN mkdir -p /root/.config/vllm/nccl/cu12/ && \
    curl -fsSL -o /root/.config/vllm/nccl/cu12/libnccl.so.2.18.1 \
        https://github.com/vllm-project/vllm-nccl/releases/download/v0.1.0/cu12-libnccl.so.2.18.1

# 安装Python AI/ML包
RUN /bin/bash -c "source /opt/miniconda/bin/activate llm_dev && \
    pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple && \
    pip config set install.trusted-host pypi.tuna.tsinghua.edu.cn && \
    pip install --no-cache-dir --upgrade pip && \
    # 安装PyTorch生态
    pip install --no-cache-dir \
        torch==2.1.2 torchvision==0.16.2 torchaudio==2.1.2 \
        --extra-index-url https://mirrors.tuna.tsinghua.edu.cn/pytorch-wheels/whl/cu121 && \
    # 安装核心数据科学包
    pip install --no-cache-dir \
        numpy pandas scikit-learn matplotlib seaborn plotly \
        scipy statsmodels && \
    # 安装核心Transformers生态
    pip install --no-cache-dir \
        transformers accelerate datasets tokenizers \
        sentence-transformers bitsandbytes \
        peft trl \
        faiss-gpu \
        langchain \
        llama-index && \
    # 安装TensorFlow生态
    pip install --no-cache-dir \
        tensorflow tensorboard && \
    # 安装核心推理工具
    pip install --no-cache-dir \
        onnx onnxruntime-gpu \
        vllm==0.2.6 && \
    # 安装TensorRT Python绑定
    pip install --no-cache-dir \
        tensorrt && \
    # 安装基础工具
    pip install --no-cache-dir \
        opencv-python pillow \
        nltk \
        xgboost \
        streamlit gradio && \
    # 安装开发工具
    pip install --no-cache-dir \
        black isort flake8 mypy pytest ipython \
        pybind11 && \
    # 安装Jupyter生态
    pip install --no-cache-dir --upgrade tornado && \
    pip install --no-cache-dir \
        jupyter jupyterlab notebook==6.4.12 \
        ipywidgets jupyter-tensorboard nbconvert && \
    # 安装Jupyter扩展
    pip install --no-cache-dir \
        jupyterlab-git \
        netron \
        gpustat nvidia-ml-py3 && \
    # 清理所有缓存
    pip cache purge && \
    conda clean -afy"

# 创建多语言开发目录结构
RUN mkdir -p /workspace/{models,datasets,checkpoints,configs,logs,scripts} && \
    mkdir -p /workspace/models/{huggingface,gguf,onnx,tensorrt} && \
    mkdir -p /workspace/datasets/{training,evaluation,inference} && \
    mkdir -p /workspace/checkpoints/{fine-tuned,lora,qlora} && \
    mkdir -p /workspace/configs/{training,inference,deployment} && \
    mkdir -p /workspace/logs/{training,inference,evaluation} && \
    mkdir -p /workspace/scripts/{training,inference,evaluation,deployment} && \
    mkdir -p /workspace/templates/{python,cpp,go,rust,cuda} && \
    mkdir -p /workspace/examples/{ai-ml,cuda-ops,go-services,cpp-performance,rust-systems} && \
    mkdir -p /workspace/projects/{libcuda_ops,fused_transformer,inferno_service} && \
    mkdir -p /workspace/go/{bin,src,pkg}

# 创建多语言项目模板
# Python AI/ML项目模板
RUN echo '# Python AI/ML项目模板' > /workspace/templates/python/README.md && \
    echo '[project]' > /workspace/templates/python/pyproject.toml && \
    echo 'name = "ai-project"' >> /workspace/templates/python/pyproject.toml && \
    echo 'version = "0.1.0"' >> /workspace/templates/python/pyproject.toml && \
    echo 'description = "AI/ML项目模板"' >> /workspace/templates/python/pyproject.toml && \
    echo 'requires-python = ">=3.10"' >> /workspace/templates/python/pyproject.toml && \
    echo 'dependencies = ["torch", "transformers", "numpy", "pandas"]' >> /workspace/templates/python/pyproject.toml

# C++高性能项目模板
RUN echo '# C++高性能项目模板' > /workspace/templates/cpp/README.md && \
    echo 'cmake_minimum_required(VERSION 3.18)' > /workspace/templates/cpp/CMakeLists.txt && \
    echo 'project(libcuda_ops LANGUAGES CXX CUDA)' >> /workspace/templates/cpp/CMakeLists.txt && \
    echo 'set(CMAKE_CXX_STANDARD 17)' >> /workspace/templates/cpp/CMakeLists.txt && \
    echo 'find_package(CUDA REQUIRED)' >> /workspace/templates/cpp/CMakeLists.txt && \
    echo 'find_package(GTest REQUIRED)' >> /workspace/templates/cpp/CMakeLists.txt

# Go微服务项目模板
RUN echo '# Go微服务项目模板' > /workspace/templates/go/README.md && \
    echo 'module inferno-service' > /workspace/templates/go/go.mod && \
    echo 'go 1.21' >> /workspace/templates/go/go.mod && \
    echo 'require (' >> /workspace/templates/go/go.mod && \
    echo '    github.com/gin-gonic/gin v1.9.1' >> /workspace/templates/go/go.mod && \
    echo '    google.golang.org/grpc v1.58.0' >> /workspace/templates/go/go.mod && \
    echo ')' >> /workspace/templates/go/go.mod

# CUDA项目模板
RUN echo '# CUDA高性能算子库模板' > /workspace/templates/cuda/README.md && \
    echo 'cmake_minimum_required(VERSION 3.18)' > /workspace/templates/cuda/CMakeLists.txt && \
    echo 'project(cuda_kernels LANGUAGES CXX CUDA)' >> /workspace/templates/cuda/CMakeLists.txt && \
    echo 'set(CMAKE_CUDA_STANDARD 17)' >> /workspace/templates/cuda/CMakeLists.txt && \
    echo 'find_package(CUDA REQUIRED)' >> /workspace/templates/cuda/CMakeLists.txt

# Rust系统项目模板
RUN echo '# Rust系统项目模板' > /workspace/templates/rust/README.md && \
    echo '[package]' > /workspace/templates/rust/Cargo.toml && \
    echo 'name = "rust-systems"' >> /workspace/templates/rust/Cargo.toml && \
    echo 'version = "0.1.0"' >> /workspace/templates/rust/Cargo.toml && \
    echo 'edition = "2021"' >> /workspace/templates/rust/Cargo.toml

# 设置Hugging Face缓存目录
ENV HF_HOME=/workspace/models/huggingface \
    HF_DATASETS_CACHE=/workspace/datasets \
    TRANSFORMERS_CACHE=/workspace/models/huggingface \
    HF_HUB_CACHE=/workspace/models/huggingface

# 创建多语言示例代码
# Python AI/ML示例
RUN echo '#!/usr/bin/env python3' > /workspace/examples/ai-ml/pytorch_example.py && \
    echo '# -*- coding: utf-8 -*-' >> /workspace/examples/ai-ml/pytorch_example.py && \
    echo 'import torch' >> /workspace/examples/ai-ml/pytorch_example.py && \
    echo '' >> /workspace/examples/ai-ml/pytorch_example.py && \
    echo 'def main():' >> /workspace/examples/ai-ml/pytorch_example.py && \
    echo '    print("🔥 PyTorch版本:", torch.__version__)' >> /workspace/examples/ai-ml/pytorch_example.py && \
    echo '    print("🚀 CUDA可用:", torch.cuda.is_available())' >> /workspace/examples/ai-ml/pytorch_example.py && \
    echo '    if torch.cuda.is_available():' >> /workspace/examples/ai-ml/pytorch_example.py && \
    echo '        print("📊 GPU数量:", torch.cuda.device_count())' >> /workspace/examples/ai-ml/pytorch_example.py && \
    echo '' >> /workspace/examples/ai-ml/pytorch_example.py && \
    echo 'if __name__ == "__main__":' >> /workspace/examples/ai-ml/pytorch_example.py && \
    echo '    main()' >> /workspace/examples/ai-ml/pytorch_example.py && \
    chmod +x /workspace/examples/ai-ml/pytorch_example.py

# CUDA算子示例
RUN echo '#include <cuda_runtime.h>' > /workspace/examples/cuda-ops/vector_add.cu && \
    echo '#include <iostream>' >> /workspace/examples/cuda-ops/vector_add.cu && \
    echo '' >> /workspace/examples/cuda-ops/vector_add.cu && \
    echo '__global__ void vectorAdd(float *a, float *b, float *c, int n) {' >> /workspace/examples/cuda-ops/vector_add.cu && \
    echo '    int idx = blockIdx.x * blockDim.x + threadIdx.x;' >> /workspace/examples/cuda-ops/vector_add.cu && \
    echo '    if (idx < n) c[idx] = a[idx] + b[idx];' >> /workspace/examples/cuda-ops/vector_add.cu && \
    echo '}' >> /workspace/examples/cuda-ops/vector_add.cu

# Go微服务示例
RUN echo 'package main' > /workspace/examples/go-services/hello_service.go && \
    echo '' >> /workspace/examples/go-services/hello_service.go && \
    echo 'import (' >> /workspace/examples/go-services/hello_service.go && \
    echo '    "github.com/gin-gonic/gin"' >> /workspace/examples/go-services/hello_service.go && \
    echo '    "net/http"' >> /workspace/examples/go-services/hello_service.go && \
    echo ')' >> /workspace/examples/go-services/hello_service.go && \
    echo '' >> /workspace/examples/go-services/hello_service.go && \
    echo 'func main() {' >> /workspace/examples/go-services/hello_service.go && \
    echo '    r := gin.Default()' >> /workspace/examples/go-services/hello_service.go && \
    echo '    r.GET("/health", func(c *gin.Context) {' >> /workspace/examples/go-services/hello_service.go && \
    echo '        c.JSON(http.StatusOK, gin.H{"status": "ok"})' >> /workspace/examples/go-services/hello_service.go && \
    echo '    })' >> /workspace/examples/go-services/hello_service.go && \
    echo '    r.Run(":8080")' >> /workspace/examples/go-services/hello_service.go && \
    echo '}' >> /workspace/examples/go-services/hello_service.go

# C++高性能示例
RUN echo '#include <chrono>' > /workspace/examples/cpp-performance/benchmark_example.cpp && \
    echo '#include <iostream>' >> /workspace/examples/cpp-performance/benchmark_example.cpp && \
    echo '#include <vector>' >> /workspace/examples/cpp-performance/benchmark_example.cpp && \
    echo '' >> /workspace/examples/cpp-performance/benchmark_example.cpp && \
    echo 'int main() {' >> /workspace/examples/cpp-performance/benchmark_example.cpp && \
    echo '    std::cout << "C++高性能计算示例" << std::endl;' >> /workspace/examples/cpp-performance/benchmark_example.cpp && \
    echo '    return 0;' >> /workspace/examples/cpp-performance/benchmark_example.cpp && \
    echo '}' >> /workspace/examples/cpp-performance/benchmark_example.cpp

# Rust系统示例
RUN echo 'fn main() {' > /workspace/examples/rust-systems/hello.rs && \
    echo '    println!("Hello from Rust!");' >> /workspace/examples/rust-systems/hello.rs && \
    echo '}' >> /workspace/examples/rust-systems/hello.rs

# 设置工作目录
WORKDIR /workspace

# 创建Jupyter配置
RUN mkdir -p /root/.jupyter && \
    echo "c.NotebookApp.ip = '0.0.0.0'" > /root/.jupyter/jupyter_notebook_config.py && \
    echo "c.NotebookApp.port = 8888" >> /root/.jupyter/jupyter_notebook_config.py && \
    echo "c.NotebookApp.open_browser = False" >> /root/.jupyter/jupyter_notebook_config.py && \
    echo "c.NotebookApp.allow_root = True" >> /root/.jupyter/jupyter_notebook_config.py && \
    echo "c.NotebookApp.token = ''" >> /root/.jupyter/jupyter_notebook_config.py

# 创建启动脚本
RUN echo '#!/bin/bash' > /root/start.sh && \
    echo 'echo "🚀 凤凰涅槃计划V3：终极AI开发环境启动中..."' >> /root/start.sh && \
    echo 'echo "=================================================="' >> /root/start.sh && \
    echo 'echo ""' >> /root/start.sh && \
    echo 'echo "📊 GPU硬件信息:"' >> /root/start.sh && \
    echo 'nvidia-smi --query-gpu=name,memory.total,memory.used,temperature.gpu --format=csv,noheader,nounits' >> /root/start.sh && \
    echo 'echo ""' >> /root/start.sh && \
    echo 'echo "🐍 Python开发环境信息:"' >> /root/start.sh && \
    echo 'source /opt/miniconda/bin/activate llm_dev' >> /root/start.sh && \
    echo 'echo "  Python: $(python --version 2>&1 | cut -d\" \" -f2)"' >> /root/start.sh && \
    echo 'python -c "import torch; print(f\"  PyTorch: {torch.__version__}\")"' >> /root/start.sh && \
    echo 'python -c "import transformers; print(f\"  Transformers: {transformers.__version__}\")"' >> /root/start.sh && \
    echo 'echo ""' >> /root/start.sh && \
    echo 'echo "🔧 C++开发环境信息:"' >> /root/start.sh && \
    echo 'echo "  GCC: $(gcc --version | head -n1)"' >> /root/start.sh && \
    echo 'echo "  Clang: $(clang --version | head -n1)"' >> /root/start.sh && \
    echo 'echo "  CMake: $(cmake --version | head -n1)"' >> /root/start.sh && \
    echo 'echo ""' >> /root/start.sh && \
    echo 'echo "🐹 Go开发环境信息:"' >> /root/start.sh && \
    echo 'echo "  Go: $(go version)"' >> /root/start.sh && \
    echo 'echo ""' >> /root/start.sh && \
    echo 'echo "🦀 Rust开发环境信息:"' >> /root/start.sh && \
    echo 'echo "  Rust: $(rustc --version)"' >> /root/start.sh && \
    echo 'echo ""' >> /root/start.sh && \
    echo 'echo "⚡ CUDA开发环境信息:"' >> /root/start.sh && \
    echo 'echo "  NVCC: $(nvcc --version | grep release)"' >> /root/start.sh && \
    echo 'echo ""' >> /root/start.sh && \
    echo 'echo "🌐 服务访问地址:"' >> /root/start.sh && \
    echo 'echo "  📓 Jupyter Lab:    http://localhost:8888"' >> /root/start.sh && \
    echo 'echo "  🔧 VS Code Web:    http://localhost:8080"' >> /root/start.sh && \
    echo 'echo "  📊 TensorBoard:    http://localhost:6006"' >> /root/start.sh && \
    echo 'echo "  🚀 vLLM API:       http://localhost:8000"' >> /root/start.sh && \
    echo 'echo ""' >> /root/start.sh && \
    echo 'echo "📁 项目目录结构:"' >> /root/start.sh && \
    echo 'echo "  /workspace/projects/libcuda_ops      - CUDA高性能算子库"' >> /root/start.sh && \
    echo 'echo "  /workspace/projects/fused_transformer - 融合型Transformer模块"' >> /root/start.sh && \
    echo 'echo "  /workspace/projects/inferno_service   - Go多后端推理服务"' >> /root/start.sh && \
    echo 'echo ""' >> /root/start.sh && \
    echo 'echo "✨ 凤凰涅槃计划V3开发环境准备完成！"' >> /root/start.sh && \
    echo 'echo "=================================================="' >> /root/start.sh && \
    echo 'exec "$@"' >> /root/start.sh && \
    chmod +x /root/start.sh

# 设置默认shell为zsh
RUN chsh -s /bin/zsh root

# 暴露端口
EXPOSE 8888 8080 6006 8000 3000 9090

# 设置健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD nvidia-smi --query-gpu=name --format=csv,noheader || exit 1

# 设置入口点
ENTRYPOINT ["/root/start.sh"]
CMD ["/bin/zsh"]