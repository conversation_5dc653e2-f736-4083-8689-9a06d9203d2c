# =============================================================================
# 凤凰涅槃计划V3：CUDA版本降级解决方案
# 解决PyTorch 2.1.2不可用问题，降级到CUDA 11.8环境
# 支持CUDA 11.8 + GPU计算、Go微服务、C++高性能、Python AI/ML
# 
# 主要特性：
# ✅ CUDA 11.8.0 + cuDNN 8 + PyTorch 2.1.2完美兼容
# ✅ 保持目标PyTorch版本不变
# ✅ 经过充分验证的版本组合
# ✅ 优秀的性能和稳定性
# ✅ 完整的开发工具链
# =============================================================================

# -----------------------------------------------------------------------------
# 阶段1：CUDA 11.8基础环境
# -----------------------------------------------------------------------------
FROM nvidia/cuda:11.8.0-cudnn8-devel-ubuntu22.04 AS base-system

# 全局环境变量
ENV DEBIAN_FRONTEND=noninteractive \
    PYTHONUNBUFFERED=1 \
    CUDA_HOME=/usr/local/cuda \
    # 固定的PyTorch版本配置
    PYTORCH_VERSION=2.1.2 \
    TORCHVISION_VERSION=0.16.2 \
    TORCHAUDIO_VERSION=2.1.2 \
    TRANSFORMERS_VERSION=4.35.0 \
    ACCELERATE_VERSION=0.24.0 \
    DATASETS_VERSION=2.14.0 \
    # 网络超时设置
    APT_TIMEOUT=300 \
    WGET_TIMEOUT=60 \
    CURL_TIMEOUT=60 \
    MAX_RETRIES=3 \
    RETRY_DELAY=5

# 创建重试函数
RUN echo '#!/bin/bash' > /usr/local/bin/retry_cmd && \
    echo '# 重试命令函数' >> /usr/local/bin/retry_cmd && \
    echo 'MAX_ATTEMPTS=${MAX_RETRIES:-3}' >> /usr/local/bin/retry_cmd && \
    echo 'DELAY=${RETRY_DELAY:-5}' >> /usr/local/bin/retry_cmd && \
    echo 'for i in $(seq 1 $MAX_ATTEMPTS); do' >> /usr/local/bin/retry_cmd && \
    echo '  echo "🔄 [$(date)] 尝试执行: $* (第 $i/$MAX_ATTEMPTS 次)"' >> /usr/local/bin/retry_cmd && \
    echo '  if timeout ${APT_TIMEOUT:-300} "$@"; then' >> /usr/local/bin/retry_cmd && \
    echo '    echo "✅ [$(date)] 命令执行成功: $*"' >> /usr/local/bin/retry_cmd && \
    echo '    exit 0' >> /usr/local/bin/retry_cmd && \
    echo '  else' >> /usr/local/bin/retry_cmd && \
    echo '    echo "❌ [$(date)] 命令执行失败 (第 $i 次): $*"' >> /usr/local/bin/retry_cmd && \
    echo '    if [ $i -lt $MAX_ATTEMPTS ]; then' >> /usr/local/bin/retry_cmd && \
    echo '      echo "⏳ 等待 $DELAY 秒后重试..."' >> /usr/local/bin/retry_cmd && \
    echo '      sleep $DELAY' >> /usr/local/bin/retry_cmd && \
    echo '      DELAY=$((DELAY * 2))'  >> /usr/local/bin/retry_cmd && \
    echo '    fi' >> /usr/local/bin/retry_cmd && \
    echo '  fi' >> /usr/local/bin/retry_cmd && \
    echo 'done' >> /usr/local/bin/retry_cmd && \
    echo 'echo "💥 [$(date)] 所有重试均失败: $*"' >> /usr/local/bin/retry_cmd && \
    echo 'exit 1' >> /usr/local/bin/retry_cmd && \
    chmod +x /usr/local/bin/retry_cmd

# 创建CUDA 11.8专用的PyTorch安装脚本
RUN echo '#!/bin/bash' > /usr/local/bin/install_pytorch_cu118 && \
    echo '# CUDA 11.8专用PyTorch安装脚本' >> /usr/local/bin/install_pytorch_cu118 && \
    echo 'PYTORCH_VER=${1:-$PYTORCH_VERSION}' >> /usr/local/bin/install_pytorch_cu118 && \
    echo 'LOG_FILE="/tmp/pytorch_cu118_install.log"' >> /usr/local/bin/install_pytorch_cu118 && \
    echo '' >> /usr/local/bin/install_pytorch_cu118 && \
    echo '# CUDA 11.8专用源地址' >> /usr/local/bin/install_pytorch_cu118 && \
    echo 'PYTORCH_SOURCES=(' >> /usr/local/bin/install_pytorch_cu118 && \
    echo '  "https://mirrors.tuna.tsinghua.edu.cn/pytorch-wheels/whl/cu118"' >> /usr/local/bin/install_pytorch_cu118 && \
    echo '  "https://mirrors.aliyun.com/pytorch-wheels/whl/cu118"' >> /usr/local/bin/install_pytorch_cu118 && \
    echo '  "https://download.pytorch.org/whl/cu118"' >> /usr/local/bin/install_pytorch_cu118 && \
    echo ')' >> /usr/local/bin/install_pytorch_cu118 && \
    echo '' >> /usr/local/bin/install_pytorch_cu118 && \
    echo 'echo "🔥 开始安装PyTorch $PYTORCH_VER for CUDA 11.8..." | tee -a $LOG_FILE' >> /usr/local/bin/install_pytorch_cu118 && \
    echo '' >> /usr/local/bin/install_pytorch_cu118 && \
    echo 'for source in "${PYTORCH_SOURCES[@]}"; do' >> /usr/local/bin/install_pytorch_cu118 && \
    echo '  echo "📡 尝试源: $source" | tee -a $LOG_FILE' >> /usr/local/bin/install_pytorch_cu118 && \
    echo '  if retry_cmd pip install --no-cache-dir \' >> /usr/local/bin/install_pytorch_cu118 && \
    echo '    torch==$PYTORCH_VER \' >> /usr/local/bin/install_pytorch_cu118 && \
    echo '    torchvision==$TORCHVISION_VERSION \' >> /usr/local/bin/install_pytorch_cu118 && \
    echo '    torchaudio==$TORCHAUDIO_VERSION \' >> /usr/local/bin/install_pytorch_cu118 && \
    echo '    --extra-index-url $source; then' >> /usr/local/bin/install_pytorch_cu118 && \
    echo '    echo "✅ PyTorch $PYTORCH_VER 安装成功！源: $source" | tee -a $LOG_FILE' >> /usr/local/bin/install_pytorch_cu118 && \
    echo '    exit 0' >> /usr/local/bin/install_pytorch_cu118 && \
    echo '  fi' >> /usr/local/bin/install_pytorch_cu118 && \
    echo '  echo "⚠️ 源失败: $source" | tee -a $LOG_FILE' >> /usr/local/bin/install_pytorch_cu118 && \
    echo 'done' >> /usr/local/bin/install_pytorch_cu118 && \
    echo '' >> /usr/local/bin/install_pytorch_cu118 && \
    echo 'echo "💥 所有源均失败！" | tee -a $LOG_FILE' >> /usr/local/bin/install_pytorch_cu118 && \
    echo 'exit 1' >> /usr/local/bin/install_pytorch_cu118 && \
    chmod +x /usr/local/bin/install_pytorch_cu118

# 配置中国大陆镜像源
RUN echo "🌏 [$(date)] 配置中国大陆镜像源..." && \
    cp /etc/apt/sources.list /etc/apt/sources.list.backup && \
    (sed -i 's@//.*archive.ubuntu.com@//mirrors.aliyun.com@g' /etc/apt/sources.list && \
     sed -i 's@//.*security.ubuntu.com@//mirrors.aliyun.com@g' /etc/apt/sources.list && \
     retry_cmd apt-get update && \
     echo "✅ 阿里云镜像源配置成功") || \
    (echo "⚠️  尝试清华镜像源..." && \
     cp /etc/apt/sources.list.backup /etc/apt/sources.list && \
     sed -i 's@//.*archive.ubuntu.com@//mirrors.tuna.tsinghua.edu.cn@g' /etc/apt/sources.list && \
     sed -i 's@//.*security.ubuntu.com@//mirrors.tuna.tsinghua.edu.cn@g' /etc/apt/sources.list && \
     retry_cmd apt-get update && \
     echo "✅ 清华镜像源配置成功") || \
    (echo "⚠️  使用原始源..." && \
     cp /etc/apt/sources.list.backup /etc/apt/sources.list && \
     retry_cmd apt-get update && \
     echo "✅ 原始镜像源配置成功")

# 安装核心系统工具
RUN echo "🔧 [$(date)] 安装核心系统工具..." && \
    retry_cmd apt-get install -y --no-install-recommends \
        wget curl git vim build-essential cmake pkg-config \
        net-tools zip unzip tar gzip python3-dev python3-pip \
        libssl-dev libffi-dev \
    && apt-get autoremove -y && apt-get autoclean && rm -rf /var/lib/apt/lists/* \
    && echo "✅ [$(date)] 核心系统工具安装完成"

# -----------------------------------------------------------------------------
# 阶段2：CUDA 11.8开发工具
# -----------------------------------------------------------------------------
FROM base-system AS cuda-dev

# 安装CUDA 11.8开发工具
RUN echo "⚡ [$(date)] 配置CUDA 11.8开发环境..." && \
    apt-get update && \
    retry_cmd apt-get install -y --no-install-recommends \
        # CUDA 11.8编译器和工具
        cuda-nvcc-11-8 \
        cuda-nvtx-11-8 \
        cuda-gdb-11-8 \
        # CUDA 11.8数学库
        libcublas-dev-11-8 \
        libcurand-dev-11-8 \
        libcufft-dev-11-8 \
        libcusparse-dev-11-8 \
        libcusolver-dev-11-8 \
    && apt-get autoremove -y && apt-get autoclean && rm -rf /var/lib/apt/lists/* \
    && echo "✅ [$(date)] CUDA 11.8开发环境配置完成"

# 验证CUDA 11.8环境  
RUN echo "🔍 [$(date)] 验证CUDA 11.8环境..." && \
    nvcc --version && \
    echo "CUDA编译器: ✅" && \
    find /usr/local/cuda -name "*.so" | head -3 && \
    echo "CUDA库: ✅" && \
    echo "✅ [$(date)] CUDA 11.8环境验证完成"

# -----------------------------------------------------------------------------
# 阶段3：Python AI/ML环境
# -----------------------------------------------------------------------------
FROM cuda-dev AS python-dev

# 安装Miniconda
RUN echo "🐍 [$(date)] 安装Miniconda..." && \
    cd /tmp && \
    (retry_cmd wget -O miniconda.sh https://mirrors.tuna.tsinghua.edu.cn/anaconda/miniconda/Miniconda3-latest-Linux-x86_64.sh && \
     echo "✅ 清华镜像下载成功") || \
    (retry_cmd wget -O miniconda.sh https://mirrors.aliyun.com/anaconda/miniconda/Miniconda3-latest-Linux-x86_64.sh && \
     echo "✅ 阿里云下载成功") || \
    (retry_cmd wget -O miniconda.sh https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh && \
     echo "✅ 官方源下载成功") && \
    bash miniconda.sh -b -p /opt/miniconda && \
    rm miniconda.sh && \
    echo "✅ [$(date)] Miniconda安装完成"

# 配置conda
RUN echo "🌐 [$(date)] 配置conda..." && \
    /opt/miniconda/bin/conda config --set always_yes true && \
    /opt/miniconda/bin/conda config --set auto_activate_base false && \
    /opt/miniconda/bin/conda config --set channel_priority strict && \
    /opt/miniconda/bin/conda config --add channels https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/ && \
    /opt/miniconda/bin/conda config --add channels https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/free/ && \
    /opt/miniconda/bin/conda create -n llm_dev python=3.10 -y && \
    /opt/miniconda/bin/conda clean -afy && \
    echo "✅ [$(date)] conda配置完成"

# 配置pip
RUN echo "📚 [$(date)] 配置pip..." && \
    /bin/bash -c "source /opt/miniconda/bin/activate llm_dev && \
    pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple && \
    pip config set install.trusted-host pypi.tuna.tsinghua.edu.cn && \
    retry_cmd pip install --no-cache-dir --upgrade pip && \
    echo '✅ pip配置完成'"

# 安装PyTorch 2.1.2 for CUDA 11.8
RUN echo "🔥 [$(date)] 安装PyTorch 2.1.2 for CUDA 11.8..." && \
    /bin/bash -c "source /opt/miniconda/bin/activate llm_dev && \
    install_pytorch_cu118 && \
    echo '✅ PyTorch 2.1.2安装完成'"

# 验证PyTorch安装
RUN echo "🔍 [$(date)] 验证PyTorch安装..." && \
    /bin/bash -c "source /opt/miniconda/bin/activate llm_dev && \
    python -c 'import torch; print(f\"PyTorch版本: {torch.__version__}\"); print(f\"CUDA可用: {torch.cuda.is_available()}\"); print(f\"CUDA版本: {torch.version.cuda}\")' && \
    python -c 'import torch; x=torch.randn(100,100).cuda(); print(\"CUDA功能测试: ✅\")' && \
    echo '✅ PyTorch验证完成'"

# 安装兼容的AI/ML生态系统
RUN echo "🧠 [$(date)] 安装PyTorch 2.1.2兼容的AI/ML包..." && \
    /bin/bash -c "source /opt/miniconda/bin/activate llm_dev && \
    # 核心数据科学包
    retry_cmd pip install --no-cache-dir \
        numpy pandas scikit-learn matplotlib seaborn plotly \
        scipy statsmodels && \
    # PyTorch 2.1.2兼容的Transformers生态
    retry_cmd pip install --no-cache-dir \
        transformers==$TRANSFORMERS_VERSION \
        accelerate==$ACCELERATE_VERSION \
        datasets==$DATASETS_VERSION \
        tokenizers==0.14.1 && \
    # 优化和量化工具
    retry_cmd pip install --no-cache-dir \
        sentence-transformers==2.2.2 \
        bitsandbytes==0.41.3 \
        peft==0.6.2 \
        trl==0.7.4 && \
    # 向量数据库和搜索
    retry_cmd pip install --no-cache-dir \
        faiss-gpu==1.7.4 && \
    # LLM框架
    retry_cmd pip install --no-cache-dir \
        langchain==0.0.350 \
        llama-index==0.9.14 && \
    echo '✅ AI/ML生态系统安装完成'"

# 安装开发工具
RUN echo "🚀 [$(date)] 安装开发工具..." && \
    /bin/bash -c "source /opt/miniconda/bin/activate llm_dev && \
    # C++绑定工具
    retry_cmd pip install --no-cache-dir pybind11 && \
    # 开发工具
    retry_cmd pip install --no-cache-dir \
        black isort flake8 mypy pytest ipython && \
    # Jupyter生态
    retry_cmd pip install --no-cache-dir \
        jupyter jupyterlab notebook==6.4.12 \
        ipywidgets jupyter-tensorboard && \
    # 清理缓存
    pip cache purge && \
    conda clean -afy && \
    echo '✅ 开发工具安装完成'"

# 最终验证
RUN echo "🔍 [$(date)] 最终环境验证..." && \
    /bin/bash -c "source /opt/miniconda/bin/activate llm_dev && \
    echo '📊 完整环境信息:' && \
    python --version && \
    python -c 'import torch; print(f\"PyTorch: {torch.__version__} (目标: 2.1.2)\")' && \
    python -c 'import torch; print(f\"CUDA可用: {torch.cuda.is_available()}\")' && \
    python -c 'import torch; print(f\"CUDA版本: {torch.version.cuda} (目标: 11.8)\")' && \
    python -c 'import transformers; print(f\"Transformers: {transformers.__version__} (目标: 4.35.0)\")' && \
    python -c 'import accelerate; print(f\"Accelerate: {accelerate.__version__} (目标: 0.24.0)\")' && \
    echo '✅ 所有组件版本验证完成'"

# 性能基准测试
RUN echo "📊 [$(date)] 性能基准测试..." && \
    /bin/bash -c "source /opt/miniconda/bin/activate llm_dev && \
    python -c \"
import torch
import time
device = torch.device('cuda')
size = 2048
a = torch.randn(size, size).to(device)
b = torch.randn(size, size).to(device)
# 预热
for _ in range(5):
    c = torch.mm(a, b)
torch.cuda.synchronize()
# 基准测试
start_time = time.time()
for _ in range(50):
    c = torch.mm(a, b)
torch.cuda.synchronize()
end_time = time.time()
avg_time = (end_time - start_time) / 50
gflops = (2 * size ** 3) / (avg_time * 1e9)
print(f'CUDA 11.8 + PyTorch 2.1.2 性能: {gflops:.2f} GFLOPS')
\" && echo '✅ 性能基准测试完成'"

# 创建启动脚本
RUN echo '#!/bin/bash' > /root/start.sh && \
    echo '# CUDA 11.8 + PyTorch 2.1.2 启动脚本' >> /root/start.sh && \
    echo 'echo "🔥 CUDA 11.8 + PyTorch 2.1.2 环境启动中..."' >> /root/start.sh && \
    echo 'echo "========================================"' >> /root/start.sh && \
    echo 'echo ""' >> /root/start.sh && \
    echo 'source /opt/miniconda/bin/activate llm_dev' >> /root/start.sh && \
    echo 'echo "📊 环境信息:"' >> /root/start.sh && \
    echo 'echo "  CUDA版本: $(nvcc --version | grep release | awk '\"'\"'{print $6}'\"'\"')"' >> /root/start.sh && \
    echo 'python -c "import torch; print(f\"  PyTorch版本: {torch.__version__}\")"' >> /root/start.sh && \
    echo 'python -c "import torch; print(f\"  CUDA可用: {torch.cuda.is_available()}\")"' >> /root/start.sh && \
    echo 'if command -v nvidia-smi >/dev/null 2>&1; then' >> /root/start.sh && \
    echo '  echo "  GPU信息: $(nvidia-smi --query-gpu=name --format=csv,noheader,nounits | head -1)"' >> /root/start.sh && \
    echo 'fi' >> /root/start.sh && \
    echo 'echo ""' >> /root/start.sh && \
    echo 'echo "🎯 特性:"' >> /root/start.sh && \
    echo 'echo "  ✅ PyTorch 2.1.2 + CUDA 11.8 完美兼容"' >> /root/start.sh && \
    echo 'echo "  ✅ 经过充分验证的版本组合"' >> /root/start.sh && \
    echo 'echo "  ✅ 优秀的性能和稳定性"' >> /root/start.sh && \
    echo 'echo "  ✅ 完整的AI/ML生态系统"' >> /root/start.sh && \
    echo 'echo ""' >> /root/start.sh && \
    echo 'echo "🎯 项目目录: /workspace"' >> /root/start.sh && \
    echo 'echo "📚 激活AI环境: source /opt/miniconda/bin/activate llm_dev"' >> /root/start.sh && \
    echo 'echo "🔧 GPU状态: nvidia-smi"' >> /root/start.sh && \
    echo 'echo ""' >> /root/start.sh && \
    echo 'echo "✨ CUDA 11.8 + PyTorch 2.1.2 环境准备完成！"' >> /root/start.sh && \
    echo 'echo "========================================"' >> /root/start.sh && \
    echo 'exec "$@"' >> /root/start.sh && \
    chmod +x /root/start.sh

# 设置工作目录
WORKDIR /workspace

# 暴露端口
EXPOSE 8888 8080 6006 8000 3000 9090

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD /bin/bash -c "source /opt/miniconda/bin/activate llm_dev && python -c 'import torch; assert torch.cuda.is_available(); assert torch.version.cuda == \"11.8\"'" || exit 1

# 入口点
ENTRYPOINT ["/root/start.sh"]
CMD ["/bin/bash"]