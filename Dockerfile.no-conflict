# =============================================================================
# 凤凰涅槃计划V3：无冲突AI开发环境
# 专门解决CUDA/cuDNN依赖冲突问题
# 
# 策略：避免apt安装cuDNN开发包，使用pip和手动安装
# =============================================================================

FROM nvidia/cuda:12.1.1-cudnn8-devel-ubuntu22.04

# 环境变量
ENV DEBIAN_FRONTEND=noninteractive \
    PYTHONUNBUFFERED=1 \
    CUDA_HOME=/usr/local/cuda \
    PATH="/opt/miniconda/bin:${PATH}"

# 显示当前CUDA/cuDNN版本信息
RUN echo "🔍 当前环境信息:" && \
    nvcc --version && \
    echo "cuDNN运行时版本:" && \
    dpkg -l | grep libcudnn8 | head -3

# 更新系统并安装基础工具
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        wget curl git vim \
        build-essential cmake \
        pkg-config \
        python3 python3-pip python3-dev \
        # 重要：不安装libcudnn8-dev，避免版本冲突
    && apt-get autoremove -y \
    && apt-get autoclean \
    && rm -rf /var/lib/apt/lists/*

# 安装Miniconda (多镜像源容错)
RUN cd /tmp && \
    # 方案1: 清华镜像
    (wget -q --timeout=60 --tries=3 https://mirrors.tuna.tsinghua.edu.cn/anaconda/miniconda/Miniconda3-latest-Linux-x86_64.sh -O miniconda.sh && \
     echo "✅ 清华镜像下载成功") || \
    # 方案2: 阿里云镜像
    (echo "⚠️ 清华镜像失败，尝试阿里云镜像..." && \
     wget -q --timeout=60 --tries=3 https://mirrors.aliyun.com/anaconda/miniconda/Miniconda3-latest-Linux-x86_64.sh -O miniconda.sh && \
     echo "✅ 阿里云镜像下载成功") || \
    # 方案3: 官方源
    (echo "⚠️ 阿里云镜像失败，尝试官方源..." && \
     wget -q --timeout=60 --tries=3 https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh -O miniconda.sh && \
     echo "✅ 官方源下载成功") && \
    bash miniconda.sh -b -p /opt/miniconda && \
    rm miniconda.sh

# 配置conda镜像源
RUN /opt/miniconda/bin/conda config --add channels https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/ && \
    /opt/miniconda/bin/conda config --add channels https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/free/ && \
    /opt/miniconda/bin/conda config --set show_channel_urls yes

# 创建Python AI环境
RUN /opt/miniconda/bin/conda create -n ai python=3.10 -y

# 安装PyTorch (通过pip，避免conda的CUDA依赖冲突)
RUN /bin/bash -c "source /opt/miniconda/bin/activate ai && \
    pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple && \
    pip install --no-cache-dir torch==2.1.2 torchvision==0.16.2 torchaudio==2.1.2 \
        --extra-index-url https://download.pytorch.org/whl/cu121"

# 安装AI/ML包 (分批安装，提高成功率)
RUN /bin/bash -c "source /opt/miniconda/bin/activate ai && \
    # 第一批：核心AI框架
    pip install --no-cache-dir \
        transformers accelerate datasets tokenizers \
        sentence-transformers && \
    # 第二批：向量搜索和LLM框架
    pip install --no-cache-dir \
        faiss-gpu langchain llama-index && \
    # 第三批：开发工具
    pip install --no-cache-dir \
        jupyter jupyterlab notebook ipywidgets \
        black isort flake8 mypy pytest && \
    # 第四批：数据科学包
    pip install --no-cache-dir \
        numpy pandas matplotlib seaborn plotly \
        scikit-learn scipy pillow opencv-python && \
    # 第五批：深度学习辅助包
    pip install --no-cache-dir \
        tqdm wandb tensorboard pydantic"

# 手动处理cuDNN开发环境 (无冲突方案)
RUN echo "🔧 配置cuDNN开发环境..." && \
    # 查找现有的cuDNN头文件
    CUDNN_HEADER=$(find /usr -name "cudnn*.h" 2>/dev/null | head -1) && \
    if [ -n "$CUDNN_HEADER" ]; then \
        echo "找到cuDNN头文件: $CUDNN_HEADER" && \
        ln -sf "$CUDNN_HEADER" /usr/include/cudnn.h; \
    else \
        echo "创建兼容的cuDNN头文件..." && \
        cat > /usr/include/cudnn.h << 'EOF'
#ifndef CUDNN_H_
#define CUDNN_H_

// cuDNN 8.9.0 兼容性定义
#define CUDNN_MAJOR 8
#define CUDNN_MINOR 9
#define CUDNN_PATCHLEVEL 0

// 基础类型定义
typedef struct cudnnContext* cudnnHandle_t;
typedef enum {
    CUDNN_STATUS_SUCCESS = 0,
    CUDNN_STATUS_NOT_INITIALIZED = 1,
    CUDNN_STATUS_ALLOC_FAILED = 2,
    CUDNN_STATUS_BAD_PARAM = 3
} cudnnStatus_t;

// 基础函数声明
cudnnStatus_t cudnnCreate(cudnnHandle_t *handle);
cudnnStatus_t cudnnDestroy(cudnnHandle_t handle);
const char* cudnnGetErrorString(cudnnStatus_t status);

#endif // CUDNN_H_
EOF
        echo "✅ 创建兼容cuDNN头文件完成"; \
    fi && \
    \
    # 确保cuDNN库链接正确
    CUDNN_LIB=$(find /usr -name "libcudnn.so*" 2>/dev/null | head -1) && \
    if [ -n "$CUDNN_LIB" ]; then \
        CUDNN_LIB_DIR=$(dirname "$CUDNN_LIB") && \
        echo "找到cuDNN库目录: $CUDNN_LIB_DIR" && \
        ln -sf "$CUDNN_LIB_DIR"/libcudnn.so* /usr/lib/x86_64-linux-gnu/ 2>/dev/null || true; \
    fi && \
    \
    # 更新动态链接库缓存
    ldconfig && \
    echo "✅ cuDNN开发环境配置完成"

# 安装TensorRT (通过pip，避免apt冲突)
RUN /bin/bash -c "source /opt/miniconda/bin/activate ai && \
    pip install --no-cache-dir tensorrt || \
    echo '⚠️ TensorRT pip安装失败，某些功能可能受限'"

# 安装Go语言 (微服务开发，多镜像源容错)
RUN cd /tmp && \
    # 方案1: 阿里云镜像
    (wget -q --timeout=60 --tries=3 https://mirrors.aliyun.com/golang/go1.21.5.linux-amd64.tar.gz && \
     echo "✅ 阿里云Go镜像下载成功") || \
    # 方案2: 清华镜像
    (echo "⚠️ 阿里云镜像失败，尝试清华镜像..." && \
     wget -q --timeout=60 --tries=3 https://mirrors.tuna.tsinghua.edu.cn/golang/go1.21.5.linux-amd64.tar.gz && \
     echo "✅ 清华Go镜像下载成功") || \
    # 方案3: 官方源
    (echo "⚠️ 清华镜像失败，尝试官方源..." && \
     wget -q --timeout=60 --tries=3 https://golang.org/dl/go1.21.5.linux-amd64.tar.gz && \
     echo "✅ 官方Go源下载成功") && \
    tar -C /usr/local -xzf go1.21.5.linux-amd64.tar.gz && \
    rm go1.21.5.linux-amd64.tar.gz

# 设置Go环境
ENV GOPATH="/workspace/go" \
    GOROOT="/usr/local/go"

RUN mkdir -p /workspace/go/{bin,src,pkg} && \
    go env -w GOPROXY=https://goproxy.cn,direct

# 安装Rust工具链 (系统编程)
RUN curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y && \
    echo 'source ~/.cargo/env' >> ~/.bashrc

# 设置Rust环境变量
ENV RUSTUP_HOME="/root/.rustup" \
    CARGO_HOME="/root/.cargo" \
    PATH="/root/.cargo/bin:/opt/miniconda/bin:/usr/local/go/bin:${PATH}"

# 配置Rust中国大陆镜像
RUN mkdir -p ~/.cargo && \
    echo '[source.crates-io]' > ~/.cargo/config.toml && \
    echo 'registry = "https://github.com/rust-lang/crates.io-index"' >> ~/.cargo/config.toml && \
    echo 'replace-with = "tuna"' >> ~/.cargo/config.toml && \
    echo '[source.tuna]' >> ~/.cargo/config.toml && \
    echo 'registry = "https://mirrors.tuna.tsinghua.edu.cn/git/crates.io-index.git"' >> ~/.cargo/config.toml

# 安装C++开发工具
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        gcc-11 g++-11 \
        clang-14 clang++-14 \
        gdb valgrind \
        libbenchmark-dev \
        protobuf-compiler libprotobuf-dev \
    && apt-get autoremove -y \
    && apt-get autoclean \
    && rm -rf /var/lib/apt/lists/*

# 创建项目目录结构
RUN mkdir -p /workspace/{projects,examples,docs} && \
    mkdir -p /workspace/projects/{libcuda_ops,fused_transformer,inferno_service}

# 创建环境验证脚本
RUN cat > /workspace/verify_environment.py << 'EOF'
#!/usr/bin/env python3
"""
凤凰涅槃计划V3环境验证脚本
"""

import sys
import subprocess

def check_cuda():
    """检查CUDA环境"""
    try:
        import torch
        print(f"✅ PyTorch: {torch.__version__}")
        print(f"✅ CUDA可用: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"✅ GPU数量: {torch.cuda.device_count()}")
            print(f"✅ GPU名称: {torch.cuda.get_device_name(0)}")
        return True
    except Exception as e:
        print(f"❌ CUDA检查失败: {e}")
        return False

def check_transformers():
    """检查Transformers"""
    try:
        import transformers
        print(f"✅ Transformers: {transformers.__version__}")
        return True
    except Exception as e:
        print(f"❌ Transformers检查失败: {e}")
        return False

def check_cuda_compile():
    """检查CUDA编译环境"""
    try:
        result = subprocess.run(['nvcc', '--version'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ CUDA编译器可用")
            return True
        else:
            print("❌ CUDA编译器不可用")
            return False
    except Exception as e:
        print(f"❌ CUDA编译器检查失败: {e}")
        return False

def check_go():
    """检查Go环境"""
    try:
        result = subprocess.run(['go', 'version'],
                              capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Go: {result.stdout.strip()}")
            return True
        else:
            print("❌ Go不可用")
            return False
    except Exception as e:
        print(f"❌ Go检查失败: {e}")
        return False

def check_rust():
    """检查Rust环境"""
    try:
        result = subprocess.run(['rustc', '--version'],
                              capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Rust: {result.stdout.strip()}")
            return True
        else:
            print("❌ Rust不可用")
            return False
    except Exception as e:
        print(f"❌ Rust检查失败: {e}")
        return False

def main():
    print("🔍 凤凰涅槃计划V3环境验证")
    print("=" * 40)
    
    checks = [
        check_cuda,
        check_transformers,
        check_cuda_compile,
        check_go,
        check_rust
    ]
    
    passed = 0
    total = len(checks)
    
    for check in checks:
        if check():
            passed += 1
        print()
    
    print(f"📊 验证结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有环境验证通过！")
        return 0
    else:
        print("⚠️ 部分环境验证失败")
        return 1

if __name__ == "__main__":
    sys.exit(main())
EOF

# 设置权限
RUN chmod +x /workspace/verify_environment.py

# 创建启动脚本
RUN cat > /workspace/start.sh << 'EOF'
#!/bin/bash

echo "🔥 凤凰涅槃计划V3：无冲突AI开发环境"
echo "========================================"
echo ""

# 激活AI环境
source /opt/miniconda/bin/activate ai

# 运行环境验证
echo "🔍 运行环境验证..."
python /workspace/verify_environment.py

echo ""
echo "📋 使用指南:"
echo "1. 激活AI环境: source /opt/miniconda/bin/activate ai"
echo "2. 启动Jupyter: jupyter lab --ip=0.0.0.0 --port=8888 --allow-root"
echo "3. CUDA编译: nvcc -I/usr/include -L/usr/lib/x86_64-linux-gnu"
echo "4. 项目目录: /workspace/projects/"
echo ""

exec "$@"
EOF

RUN chmod +x /workspace/start.sh

# 设置工作目录
WORKDIR /workspace

# 暴露端口
EXPOSE 8888 8080 6006

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD /bin/bash -c "source /opt/miniconda/bin/activate ai && python -c 'import torch; assert torch.cuda.is_available()'" || exit 1

# 入口点
ENTRYPOINT ["/workspace/start.sh"]
CMD ["/bin/bash"]
