# 凤凰涅槃计划V3：终极AI开发环境 (CUDA安全版本)
# 支持CUDA GPU计算、Go微服务、C++高性能、Python AI/ML
FROM nvidia/cuda:12.1.1-cudnn8-devel-ubuntu22.04

# 设置环境变量
ENV DEBIAN_FRONTEND=noninteractive \
    PYTHONUNBUFFERED=1 \
    CUDA_HOME=/usr/local/cuda \
    PATH="/opt/miniconda/bin:/usr/local/go/bin:/root/.cargo/bin:${PATH}" \
    GOPATH="/workspace/go" \
    GOROOT="/usr/local/go" \
    RUSTUP_HOME="/root/.rustup" \
    CARGO_HOME="/root/.cargo"

# 配置中国大陆镜像源
RUN sed -i 's@//.*archive.ubuntu.com@//mirrors.aliyun.com@g' /etc/apt/sources.list && \
    sed -i 's@//.*security.ubuntu.com@//mirrors.aliyun.com@g' /etc/apt/sources.list

# 安装系统依赖 (基础开发工具)
RUN apt-get update && apt-get install -y \
        # 基础工具
        wget curl git vim nano tree htop \
        build-essential cmake ninja-build \
        pkg-config autoconf automake libtool \
        # 网络和压缩工具
        net-tools iputils-ping \
        zip unzip tar gzip \
        # 开发库
        libssl-dev libffi-dev \
        libxml2-dev libxslt1-dev \
        libjpeg-dev libpng-dev \
        # 系统监控
        nvidia-utils-525 \
    && apt-get autoremove -y \
    && apt-get autoclean \
    && rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

# 安装C++高性能开发工具
RUN apt-get update && apt-get install -y \
        # 现代C++编译器
        gcc-11 g++-11 clang-14 clang++-14 \
        # 构建工具
        make cmake ninja-build \
        # 代码格式化和检查
        clang-format-14 clang-tidy-14 \
        # 性能测试和调试
        libbenchmark-dev libgtest-dev \
        gdb valgrind \
        # Protocol Buffers (Go微服务需要)
        protobuf-compiler libprotobuf-dev \
    && apt-get autoremove -y \
    && apt-get autoclean \
    && rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

# 安装CUDA开发工具 (保守策略，避免依赖冲突)
RUN apt-get update && \
    # 只安装核心CUDA开发组件
    apt-get install -y --no-install-recommends \
        # CUDA编译器
        cuda-nvcc-12-1 \
        # CUDA工具
        cuda-nvtx-12-1 \
        cuda-gdb-12-1 \
        # 核心数学库
        libcublas-dev-12-1 \
        libcurand-dev-12-1 \
        libcufft-dev-12-1 \
        # 稀疏矩阵库
        libcusparse-dev-12-1 \
        libcusolver-dev-12-1 \
    && apt-get autoremove -y \
    && apt-get autoclean \
    && rm -rf /var/lib/apt/lists/*

# 验证CUDA环境 (不强制安装cuDNN开发包)
RUN echo "🔍 验证CUDA环境..." && \
    nvcc --version && \
    echo "✅ NVCC编译器可用" && \
    find /usr/local/cuda -name "*.so" | head -5 && \
    echo "✅ CUDA库检查完成" && \
    # 检查cuDNN运行时 (基础镜像已提供)
    find /usr -name "libcudnn*" 2>/dev/null | head -3 && \
    echo "✅ cuDNN运行时可用 (开发包将通过Python安装)"

# 安装Go语言
RUN wget -O /tmp/go.tar.gz https://mirrors.aliyun.com/golang/go1.21.5.linux-amd64.tar.gz && \
    tar -C /usr/local -xzf /tmp/go.tar.gz && \
    rm /tmp/go.tar.gz && \
    # 创建Go工作目录
    mkdir -p /workspace/go/{bin,src,pkg} && \
    # 配置Go代理 (中国大陆优化)
    go env -w GOPROXY=https://goproxy.cn,direct && \
    go env -w GOSUMDB=sum.golang.google.cn && \
    # 安装Go工具
    go install golang.org/x/tools/gopls@latest && \
    go install github.com/go-delve/delve/cmd/dlv@latest && \
    go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest && \
    go install google.golang.org/protobuf/cmd/protoc-gen-go@latest && \
    go install google.golang.org/grpc/cmd/protoc-gen-go-grpc@latest

# 安装Rust工具链
RUN curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y && \
    source /root/.cargo/env && \
    # 配置中国大陆镜像
    echo '[source.crates-io]' > /root/.cargo/config.toml && \
    echo 'registry = "https://github.com/rust-lang/crates.io-index"' >> /root/.cargo/config.toml && \
    echo 'replace-with = "tuna"' >> /root/.cargo/config.toml && \
    echo '[source.tuna]' >> /root/.cargo/config.toml && \
    echo 'registry = "https://mirrors.tuna.tsinghua.edu.cn/git/crates.io-index.git"' >> /root/.cargo/config.toml && \
    # 安装Rust组件
    rustup component add rust-analyzer && \
    rustup component add clippy && \
    rustup component add rustfmt && \
    cargo install cargo-watch

# 安装Miniconda (Python环境)
RUN wget -O /tmp/miniconda.sh https://mirrors.tuna.tsinghua.edu.cn/anaconda/miniconda/Miniconda3-latest-Linux-x86_64.sh && \
    bash /tmp/miniconda.sh -b -p /opt/miniconda && \
    rm /tmp/miniconda.sh && \
    # 配置conda中国大陆镜像
    /opt/miniconda/bin/conda config --add channels https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/ && \
    /opt/miniconda/bin/conda config --add channels https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/free/ && \
    /opt/miniconda/bin/conda config --add channels https://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud/conda-forge/ && \
    /opt/miniconda/bin/conda config --set show_channel_urls yes && \
    # 创建AI开发环境
    /opt/miniconda/bin/conda create -n llm_dev python=3.10 -y && \
    /opt/miniconda/bin/conda clean -afy

# 安装Python AI/ML包 (包含TensorRT Python绑定)
RUN /bin/bash -c "source /opt/miniconda/bin/activate llm_dev && \
    pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple && \
    pip config set install.trusted-host pypi.tuna.tsinghua.edu.cn && \
    pip install --no-cache-dir --upgrade pip && \
    # 安装PyTorch生态
    pip install --no-cache-dir \
        torch==2.1.2 torchvision==0.16.2 torchaudio==2.1.2 \
        --extra-index-url https://mirrors.tuna.tsinghua.edu.cn/pytorch-wheels/whl/cu121 && \
    # 安装核心AI/ML包
    pip install --no-cache-dir \
        transformers accelerate datasets tokenizers \
        sentence-transformers bitsandbytes \
        peft trl faiss-gpu \
        langchain llama-index && \
    # 安装TensorRT Python绑定 (替代apt安装)
    pip install --no-cache-dir \
        tensorrt && \
    # 安装开发工具 (包含pybind11)
    pip install --no-cache-dir \
        pybind11 black isort flake8 mypy pytest ipython && \
    # 安装Jupyter生态
    pip install --no-cache-dir \
        jupyter jupyterlab notebook==6.4.12 \
        ipywidgets jupyter-tensorboard && \
    # 清理缓存
    pip cache purge && \
    conda clean -afy"

# 安装Oh My Zsh和Starship
RUN sh -c "$(wget -O- https://gitee.com/pocmon/ohmyzsh/raw/master/tools/install.sh)" && \
    wget -O /tmp/starship.tar.gz https://github.com/starship/starship/releases/latest/download/starship-x86_64-unknown-linux-gnu.tar.gz && \
    tar -xzf /tmp/starship.tar.gz -C /tmp && \
    mv /tmp/starship /usr/local/bin/ && \
    rm /tmp/starship.tar.gz

# 配置shell环境
RUN sed -i 's/ZSH_THEME="robbyrussell"/ZSH_THEME=""/g' ~/.zshrc && \
    echo 'plugins=(git conda-env docker python pip golang rust zsh-autosuggestions zsh-syntax-highlighting)' >> ~/.zshrc && \
    echo 'eval "$(starship init zsh)"' >> ~/.zshrc && \
    echo 'export GOPATH=/workspace/go' >> ~/.zshrc && \
    echo 'export GOROOT=/usr/local/go' >> ~/.zshrc && \
    echo 'alias gpu-status="nvidia-smi"' >> ~/.zshrc && \
    echo 'alias nv="nvcc"' >> ~/.zshrc

# 创建项目目录结构
RUN mkdir -p /workspace/{projects,templates,examples} && \
    mkdir -p /workspace/projects/{libcuda_ops,fused_transformer,inferno_service} && \
    mkdir -p /workspace/templates/{python,cpp,go,cuda,rust} && \
    mkdir -p /workspace/examples/{ai-ml,cuda-ops,go-services,cpp-performance}

# 创建启动脚本
RUN echo '#!/bin/bash' > /root/start.sh && \
    echo 'echo "🔥 凤凰涅槃计划V3：终极AI开发环境"' >> /root/start.sh && \
    echo 'echo "CUDA: $(nvcc --version | grep release)"' >> /root/start.sh && \
    echo 'echo "Go: $(go version)"' >> /root/start.sh && \
    echo 'echo "Rust: $(rustc --version)"' >> /root/start.sh && \
    echo 'source /opt/miniconda/bin/activate llm_dev' >> /root/start.sh && \
    echo 'python -c "import torch; print(f\"PyTorch: {torch.__version__}\")"' >> /root/start.sh && \
    echo 'exec "$@"' >> /root/start.sh && \
    chmod +x /root/start.sh

# 设置工作目录
WORKDIR /workspace

# 暴露端口
EXPOSE 8888 8080 6006 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD nvidia-smi --query-gpu=name --format=csv,noheader || exit 1

# 入口点
ENTRYPOINT ["/root/start.sh"]
CMD ["/bin/zsh"]
