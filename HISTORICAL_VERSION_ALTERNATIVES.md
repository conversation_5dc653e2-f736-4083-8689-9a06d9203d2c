# PyTorch历史版本获取替代方案

## 🔍 问题背景

当官方PyPI和镜像源不再提供特定版本的PyTorch wheel包时，我们需要通过替代方案来获取历史版本。这种情况常见于：
- 旧版本因安全问题被下架
- 官方仅维护最新几个版本
- 镜像源同步策略变更
- 特定CUDA版本的wheel包不再维护

## 🛠️ 替代方案详解

### 方案1：源码编译安装 ⭐⭐⭐⭐⭐

**优势**：完全控制版本和编译选项，支持任意历史版本
**劣势**：编译时间长，依赖复杂
**适用场景**：需要精确版本控制的生产环境

#### 实现步骤

```bash
#!/bin/bash
# PyTorch 2.1.2 源码编译脚本

# 1. 准备编译环境
export CUDA_HOME=/usr/local/cuda
export CUDNN_LIB_DIR=/usr/lib/x86_64-linux-gnu
export CUDNN_INCLUDE_DIR=/usr/include
export MAX_JOBS=4

# 2. 克隆源码
git clone --recursive https://github.com/pytorch/pytorch.git
cd pytorch
git checkout v2.1.2
git submodule sync
git submodule update --init --recursive

# 3. 设置编译选项
export CMAKE_PREFIX_PATH=${CONDA_PREFIX:-$(python -c 'import sys; print(sys.prefix)')}
export USE_CUDA=1
export USE_CUDNN=1
export TORCH_CUDA_ARCH_LIST="6.1;7.0;7.5;8.0;8.6"

# 4. 编译安装
python setup.py develop
```

#### Docker化源码编译

```dockerfile
# Dockerfile.pytorch-from-source
FROM nvidia/cuda:12.1.1-cudnn8-devel-ubuntu22.04

# 安装编译依赖
RUN apt-get update && apt-get install -y \
    git cmake ninja-build \
    libopenmpi-dev libomp-dev \
    python3-dev python3-pip

# 设置编译环境
ENV MAX_JOBS=4
ENV CUDA_HOME=/usr/local/cuda
ENV USE_CUDA=1
ENV USE_CUDNN=1
ENV TORCH_CUDA_ARCH_LIST="6.1;7.0;7.5;8.0;8.6"

# 编译PyTorch 2.1.2
RUN git clone --recursive https://github.com/pytorch/pytorch.git && \
    cd pytorch && \
    git checkout v2.1.2 && \
    git submodule sync && \
    git submodule update --init --recursive && \
    python setup.py develop

# 编译torchvision
RUN git clone https://github.com/pytorch/vision.git && \
    cd vision && \
    git checkout v0.16.2 && \
    python setup.py develop

# 编译torchaudio
RUN git clone https://github.com/pytorch/audio.git && \
    cd audio && \
    git checkout v2.1.2 && \
    python setup.py develop
```

### 方案2：第三方Wheel仓库 ⭐⭐⭐⭐

**优势**：安装快速，无需编译
**劣势**：安全风险，版本可能不完整
**适用场景**：开发测试环境

#### conda-forge渠道

```bash
# 通过conda-forge获取历史版本
conda install pytorch=2.1.2 torchvision=0.16.2 torchaudio=2.1.2 \
    pytorch-cuda=11.8 -c pytorch -c nvidia -c conda-forge

# 或指定特定构建
conda install pytorch=2.1.2=py310_cuda11.8_cudnn8.7.0_0 -c pytorch
```

#### PyTorch官方归档

```bash
# 查找官方归档版本
pip install torch==2.1.2 torchvision==0.16.2 torchaudio==2.1.2 \
    --index-url https://download.pytorch.org/whl/cu118 \
    --trusted-host download.pytorch.org
```

#### 中科院软件源（中国大陆）

```bash
# 中科院可能保留更多历史版本
pip install torch==2.1.2 torchvision==0.16.2 torchaudio==2.1.2 \
    --index-url https://pypi.mirrors.ustc.edu.cn/simple/ \
    --trusted-host pypi.mirrors.ustc.edu.cn
```

### 方案3：Docker镜像缓存 ⭐⭐⭐

**优势**：环境一致性好，版本固定
**劣势**：镜像体积大，更新困难
**适用场景**：长期项目维护

#### 官方历史镜像

```bash
# 使用PyTorch官方历史镜像
docker pull pytorch/pytorch:2.1.2-cuda11.8-cudnn8-devel

# 或使用特定日期的镜像
docker pull pytorch/pytorch:2.1.2-cuda11.8-cudnn8-devel-ubuntu20.04
```

#### 自建镜像缓存

```yaml
# docker-compose.yml for image caching
version: '3.8'
services:
  pytorch-212:
    image: pytorch/pytorch:2.1.2-cuda11.8-cudnn8-devel
    container_name: pytorch-212-cache
    volumes:
      - pytorch-212-cache:/workspace
    command: sleep infinity

volumes:
  pytorch-212-cache:
    driver: local
```

### 方案4：Wheel文件本地缓存 ⭐⭐⭐⭐

**优势**：离线安装，安全可控
**劣势**：需要提前下载和维护
**适用场景**：企业内网环境

#### 创建本地Wheel缓存

```bash
#!/bin/bash
# 创建PyTorch wheel缓存脚本

CACHE_DIR="./pytorch-wheels-cache"
PYTORCH_VERSION="2.1.2"
CUDA_VERSION="cu118"

mkdir -p "$CACHE_DIR"

# 下载PyTorch wheel文件
pip download torch==$PYTORCH_VERSION \
    --index-url https://download.pytorch.org/whl/$CUDA_VERSION \
    --dest "$CACHE_DIR" \
    --no-deps

pip download torchvision==0.16.2 \
    --index-url https://download.pytorch.org/whl/$CUDA_VERSION \
    --dest "$CACHE_DIR" \
    --no-deps

pip download torchaudio==$PYTORCH_VERSION \
    --index-url https://download.pytorch.org/whl/$CUDA_VERSION \
    --dest "$CACHE_DIR" \
    --no-deps

# 从本地缓存安装
pip install torch==$PYTORCH_VERSION torchvision==0.16.2 torchaudio==$PYTORCH_VERSION \
    --find-links "$CACHE_DIR" \
    --no-index
```

#### Docker集成本地缓存

```dockerfile
# 使用本地wheel缓存的Dockerfile
FROM nvidia/cuda:12.1.1-cudnn8-devel-ubuntu22.04

# 复制wheel缓存
COPY pytorch-wheels-cache /tmp/pytorch-wheels

# 从本地缓存安装
RUN pip install torch==2.1.2 torchvision==0.16.2 torchaudio==2.1.2 \
    --find-links /tmp/pytorch-wheels \
    --no-index \
    --no-deps
```

### 方案5：虚拟环境快照 ⭐⭐⭐

**优势**：环境完整复制，依赖关系清晰
**劣势**：平台依赖性强
**适用场景**：环境迁移和备份

#### 环境导出和恢复

```bash
# 导出完整环境
conda env export > pytorch-212-environment.yml

# 或使用pip
pip freeze > pytorch-212-requirements.txt

# 恢复环境
conda env create -f pytorch-212-environment.yml

# 或使用pip
pip install -r pytorch-212-requirements.txt
```

#### 环境快照脚本

```bash
#!/bin/bash
# 环境快照和恢复脚本

create_snapshot() {
    local snapshot_name=$1
    local snapshot_dir="./env-snapshots/$snapshot_name"
    
    mkdir -p "$snapshot_dir"
    
    # 导出conda环境
    conda env export > "$snapshot_dir/environment.yml"
    
    # 导出pip包列表
    pip freeze > "$snapshot_dir/requirements.txt"
    
    # 记录系统信息
    echo "Created: $(date)" > "$snapshot_dir/info.txt"
    echo "Python: $(python --version)" >> "$snapshot_dir/info.txt"
    echo "CUDA: $(nvcc --version 2>/dev/null || echo 'Not available')" >> "$snapshot_dir/info.txt"
    
    echo "Environment snapshot saved to: $snapshot_dir"
}

restore_snapshot() {
    local snapshot_name=$1
    local snapshot_dir="./env-snapshots/$snapshot_name"
    
    if [[ ! -d "$snapshot_dir" ]]; then
        echo "Error: Snapshot not found: $snapshot_dir"
        return 1
    fi
    
    echo "Restoring environment from: $snapshot_dir"
    
    # 恢复conda环境
    if [[ -f "$snapshot_dir/environment.yml" ]]; then
        conda env create -f "$snapshot_dir/environment.yml" -n "restored-$snapshot_name"
    fi
    
    # 或恢复pip环境
    if [[ -f "$snapshot_dir/requirements.txt" ]]; then
        pip install -r "$snapshot_dir/requirements.txt"
    fi
}

# 使用示例
# create_snapshot "pytorch-212-working"
# restore_snapshot "pytorch-212-working"
```

## 🔧 自动化获取脚本

### 智能版本获取器

```python
#!/usr/bin/env python3
"""
PyTorch历史版本智能获取器
支持多种获取方式和自动降级
"""
import subprocess
import sys
import os
import requests
from packaging import version

class PyTorchVersionManager:
    def __init__(self):
        self.sources = [
            {
                "name": "PyTorch官方",
                "url_template": "https://download.pytorch.org/whl/cu{cuda_version}/torch-{pytorch_version}%2Bcu{cuda_version}-cp{python_version}-cp{python_version}-linux_x86_64.whl"
            },
            {
                "name": "清华镜像",
                "url_template": "https://mirrors.tuna.tsinghua.edu.cn/pytorch-wheels/whl/cu{cuda_version}/torch-{pytorch_version}%2Bcu{cuda_version}-cp{python_version}-cp{python_version}-linux_x86_64.whl"
            },
            {
                "name": "阿里云镜像", 
                "url_template": "https://mirrors.aliyun.com/pytorch-wheels/whl/cu{cuda_version}/torch-{pytorch_version}%2Bcu{cuda_version}-cp{python_version}-cp{python_version}-linux_x86_64.whl"
            }
        ]
        
        self.fallback_versions = [
            "2.1.2", "2.1.1", "2.1.0",
            "2.0.1", "2.0.0",
            "1.13.1", "1.13.0"
        ]
    
    def check_url_exists(self, url):
        """检查URL是否存在"""
        try:
            response = requests.head(url, timeout=10)
            return response.status_code == 200
        except:
            return False
    
    def get_python_version(self):
        """获取Python版本"""
        version_info = sys.version_info
        return f"{version_info.major}{version_info.minor}"
    
    def find_available_version(self, target_version, cuda_version="118"):
        """查找可用的PyTorch版本"""
        python_ver = self.get_python_version()
        
        # 尝试目标版本
        versions_to_try = [target_version] + self.fallback_versions
        
        for pytorch_ver in versions_to_try:
            for source in self.sources:
                url = source["url_template"].format(
                    pytorch_version=pytorch_ver,
                    cuda_version=cuda_version,
                    python_version=python_ver
                )
                
                print(f"🔍 检查 {source['name']}: PyTorch {pytorch_ver}")
                
                if self.check_url_exists(url):
                    print(f"✅ 找到可用版本: {pytorch_ver} from {source['name']}")
                    return pytorch_ver, source, url
                else:
                    print(f"❌ 不可用: {pytorch_ver} from {source['name']}")
        
        return None, None, None
    
    def install_from_source(self, version):
        """从源码编译安装"""
        print(f"🔧 开始从源码编译PyTorch {version}...")
        
        commands = [
            "git clone --recursive https://github.com/pytorch/pytorch.git",
            f"cd pytorch && git checkout v{version}",
            "cd pytorch && git submodule sync",
            "cd pytorch && git submodule update --init --recursive",
            "cd pytorch && python setup.py develop"
        ]
        
        for cmd in commands:
            print(f"执行: {cmd}")
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            if result.returncode != 0:
                print(f"❌ 命令失败: {cmd}")
                print(f"错误输出: {result.stderr}")
                return False
        
        print(f"✅ PyTorch {version} 源码编译完成")
        return True
    
    def install_pytorch(self, target_version="2.1.2", cuda_version="118", allow_source_build=False):
        """安装PyTorch"""
        print(f"🎯 目标版本: PyTorch {target_version}, CUDA {cuda_version}")
        
        # 方案1: 查找预编译wheel
        found_version, source, url = self.find_available_version(target_version, cuda_version)
        
        if found_version:
            print(f"📦 使用预编译包安装: {found_version}")
            install_cmd = f"pip install {url}"
            result = subprocess.run(install_cmd, shell=True)
            return result.returncode == 0
        
        # 方案2: 从源码编译
        if allow_source_build:
            print("📦 预编译包不可用，尝试源码编译...")
            return self.install_from_source(target_version)
        else:
            print("❌ 未找到可用版本，且未启用源码编译")
            return False

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="PyTorch历史版本获取器")
    parser.add_argument("--version", default="2.1.2", help="目标PyTorch版本")
    parser.add_argument("--cuda", default="118", help="CUDA版本 (118, 121等)")
    parser.add_argument("--allow-source", action="store_true", help="允许源码编译")
    
    args = parser.parse_args()
    
    manager = PyTorchVersionManager()
    success = manager.install_pytorch(args.version, args.cuda, args.allow_source)
    
    if success:
        print("🎉 PyTorch安装成功！")
        # 验证安装
        try:
            import torch
            print(f"✅ 验证成功: PyTorch {torch.__version__}")
        except ImportError:
            print("❌ 验证失败: 无法导入torch")
    else:
        print("💥 PyTorch安装失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
```

## 📋 选择建议

### 根据场景选择方案

| 场景 | 推荐方案 | 理由 |
|------|----------|------|
| 生产环境 | 源码编译 | 版本精确控制，安全可靠 |
| 开发测试 | 第三方Wheel | 安装快速，迭代方便 |
| 离线环境 | 本地缓存 | 无网络依赖，安装稳定 |
| 环境迁移 | Docker镜像 | 环境一致性，部署简单 |
| 临时使用 | conda-forge | 版本选择多，安装简单 |

### 安全性考虑

1. **源码编译**: 最安全，但需要验证源码完整性
2. **官方镜像**: 安全性高，推荐使用
3. **第三方源**: 需要验证校验和，谨慎使用
4. **本地缓存**: 安全性取决于缓存来源

### 实施步骤

1. **评估需求**: 确定必须使用的PyTorch版本和原因
2. **选择方案**: 根据环境和安全要求选择获取方案
3. **测试验证**: 在测试环境验证安装和功能
4. **文档记录**: 记录获取过程和验证结果
5. **监控维护**: 定期检查版本可用性和安全更新

---

**最后更新**: 2025-07-31  
**适用场景**: PyTorch历史版本获取  
**维护者**: AI开发团队