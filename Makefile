# Makefile for 凤凰涅槃计划V3：终极AI开发环境
# 支持CUDA GPU计算、Go微服务、C++高性能、Python AI/ML

.PHONY: help setup build start stop restart enter logs clean rebuild status health

# 默认目标
.DEFAULT_GOAL := help

# 项目配置
PROJECT_NAME := multi-lang
COMPOSE_FILE := docker-compose.yml
SERVICE_NAME := multi-lang

# 颜色定义
BLUE := \033[36m
GREEN := \033[32m
YELLOW := \033[33m
RED := \033[31m
NC := \033[0m

# 帮助信息
help: ## 显示帮助信息
	@echo "$(BLUE)凤凰涅槃计划V3：终极AI开发环境管理工具$(NC)"
	@echo "=============================================="
	@echo ""
	@echo "$(GREEN)可用命令:$(NC)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(YELLOW)%-15s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST)
	@echo ""
	@echo "$(GREEN)快速开始:$(NC)"
	@echo "  make setup    # 初始化环境"
	@echo "  make start    # 启动开发环境"
	@echo "  make enter    # 进入开发环境"

# 环境检查
check-china: ## 检查中国大陆网络环境
	@echo "$(BLUE)检查网络环境...$(NC)"
	@curl -s --connect-timeout 5 https://mirrors.tuna.tsinghua.edu.cn > /dev/null && \
		echo "$(GREEN)✅ 中国大陆网络环境检查通过$(NC)" || \
		(echo "$(RED)❌ 无法访问中国大陆镜像源$(NC)" && exit 1)

check-docker: ## 检查Docker环境
	@echo "$(BLUE)检查Docker环境...$(NC)"
	@command -v docker >/dev/null 2>&1 || (echo "$(RED)❌ Docker未安装$(NC)" && exit 1)
	@command -v docker-compose >/dev/null 2>&1 || docker compose version >/dev/null 2>&1 || \
		(echo "$(RED)❌ Docker Compose未安装$(NC)" && exit 1)
	@echo "$(GREEN)✅ Docker环境检查通过$(NC)"

check-gpu: ## 检查GPU支持
	@echo "$(BLUE)检查GPU支持...$(NC)"
	@if command -v nvidia-smi >/dev/null 2>&1; then \
		if docker run --rm --gpus all nvidia/cuda:11.0-base nvidia-smi >/dev/null 2>&1; then \
			echo "$(GREEN)✅ NVIDIA Docker支持已启用$(NC)"; \
		else \
			echo "$(YELLOW)⚠️ NVIDIA Docker支持未启用$(NC)"; \
		fi; \
	else \
		echo "$(YELLOW)⚠️ 未检测到NVIDIA GPU$(NC)"; \
	fi

# 环境设置
setup: check-china check-docker ## 初始化开发环境
	@echo "$(BLUE)初始化开发环境...$(NC)"
	@chmod +x setup-environment.sh
	@./setup-environment.sh
	@echo "$(GREEN)✅ 环境初始化完成$(NC)"

# 构建镜像
build: check-china ## 构建Docker镜像
	@echo "$(BLUE)构建Docker镜像...$(NC)"
	@docker-compose build --no-cache $(SERVICE_NAME)
	@echo "$(GREEN)✅ 镜像构建完成$(NC)"

# 启动服务
start: ## 启动开发环境
	@echo "$(BLUE)启动开发环境...$(NC)"
	@docker-compose up -d $(SERVICE_NAME)
	@echo "$(GREEN)✅ 环境启动完成$(NC)"
	@echo ""
	@echo "$(GREEN)📋 可用服务:$(NC)"
	@echo "  🔗 Jupyter Lab:    http://localhost:8888"
	@echo "  🔗 VS Code Web:    http://localhost:8080"
	@echo "  🔗 TensorBoard:    http://localhost:6006"
	@echo "  🔗 vLLM API:       http://localhost:8000"

# 启动所有服务 (包括可选服务)
start-all: ## 启动所有服务 (包括数据库、缓存等)
	@echo "$(BLUE)启动所有服务...$(NC)"
	@docker-compose --profile cache --profile database --profile storage up -d
	@echo "$(GREEN)✅ 所有服务启动完成$(NC)"

# 停止服务
stop: ## 停止开发环境
	@echo "$(BLUE)停止开发环境...$(NC)"
	@docker-compose down
	@echo "$(GREEN)✅ 环境已停止$(NC)"

# 重启服务
restart: stop start ## 重启开发环境

# 进入开发环境
enter: ## 进入开发环境
	@echo "$(BLUE)进入AI开发环境...$(NC)"
	@docker-compose exec $(SERVICE_NAME) zsh

# 查看日志
logs: ## 查看服务日志
	@echo "$(BLUE)查看环境日志...$(NC)"
	@docker-compose logs -f $(SERVICE_NAME)

# 查看状态
status: ## 查看服务状态
	@echo "$(BLUE)服务状态:$(NC)"
	@docker-compose ps

# 健康检查
health: ## 检查服务健康状态
	@echo "$(BLUE)健康检查:$(NC)"
	@docker-compose exec $(SERVICE_NAME) nvidia-smi --query-gpu=name,memory.total,memory.used,temperature.gpu --format=csv,noheader,nounits || echo "$(YELLOW)GPU信息获取失败$(NC)"

# 重建环境
rebuild: ## 重建开发环境
	@echo "$(BLUE)重建开发环境...$(NC)"
	@docker-compose down
	@docker-compose build --no-cache $(SERVICE_NAME)
	@docker-compose up -d $(SERVICE_NAME)
	@echo "$(GREEN)✅ 环境重建完成$(NC)"

# 清理资源
clean: ## 清理Docker资源
	@echo "$(BLUE)清理Docker资源...$(NC)"
	@docker-compose down --volumes --remove-orphans
	@docker system prune -f
	@echo "$(GREEN)✅ 清理完成$(NC)"

# 深度清理
clean-all: ## 深度清理所有Docker资源
	@echo "$(BLUE)深度清理Docker资源...$(NC)"
	@docker-compose down --volumes --remove-orphans
	@docker system prune -a -f
	@echo "$(GREEN)✅ 深度清理完成$(NC)"

# 备份数据
backup: ## 备份重要数据
	@echo "$(BLUE)备份数据...$(NC)"
	@mkdir -p backups
	@tar -czf backups/ai-dev-backup-$(shell date +%Y%m%d-%H%M%S).tar.gz data/ workspace/ .env || true
	@echo "$(GREEN)✅ 数据备份完成$(NC)"

# 恢复数据
restore: ## 恢复数据 (需要指定备份文件: make restore BACKUP=filename)
	@echo "$(BLUE)恢复数据...$(NC)"
	@if [ -z "$(BACKUP)" ]; then \
		echo "$(RED)❌ 请指定备份文件: make restore BACKUP=filename$(NC)"; \
		exit 1; \
	fi
	@tar -xzf $(BACKUP)
	@echo "$(GREEN)✅ 数据恢复完成$(NC)"

# 更新镜像
update: ## 更新基础镜像
	@echo "$(BLUE)更新基础镜像...$(NC)"
	@docker pull nvidia/cuda:12.1.1-cudnn8-devel-ubuntu22.04
	@echo "$(GREEN)✅ 基础镜像更新完成$(NC)"

# 开发模式
dev: ## 启动开发模式 (挂载当前目录)
	@echo "$(BLUE)启动开发模式...$(NC)"
	@docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d $(SERVICE_NAME)
	@echo "$(GREEN)✅ 开发模式启动完成$(NC)"

# 生产模式
prod: ## 启动生产模式
	@echo "$(BLUE)启动生产模式...$(NC)"
	@docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
	@echo "$(GREEN)✅ 生产模式启动完成$(NC)"

# 监控资源
monitor: ## 监控资源使用情况
	@echo "$(BLUE)监控资源使用情况...$(NC)"
	@watch -n 2 'docker stats --no-stream ai-dev-environment'

# 快速开始
quick-start: setup start enter ## 快速开始 (设置+启动+进入)

# 显示环境信息
info: ## 显示环境信息
	@echo "$(BLUE)环境信息:$(NC)"
	@echo "  项目名称: $(PROJECT_NAME)"
	@echo "  服务名称: $(SERVICE_NAME)"
	@echo "  配置文件: $(COMPOSE_FILE)"
	@echo "  Docker版本: $(shell docker --version)"
	@echo "  Docker Compose版本: $(shell docker-compose --version 2>/dev/null || docker compose version)"
	@if command -v nvidia-smi >/dev/null 2>&1; then \
		echo "  GPU信息: $(shell nvidia-smi --query-gpu=name --format=csv,noheader,nounits | head -1)"; \
	fi
