# 📄 学习计划PDF转换指南

## 🎯 转换目标
将 [`AI_Engineer_90Day_Master_Plan.md`](AI_Engineer_90Day_Master_Plan.md) 转换为高质量的PDF文档，便于打印和离线阅读。

---

## 🔧 方法一：使用Pandoc（推荐）

### 安装Pandoc
```bash
# Windows (使用Chocolatey)
choco install pandoc

# macOS (使用Homebrew)
brew install pandoc

# Ubuntu/Debian
sudo apt-get install pandoc

# 或下载官方安装包
# https://pandoc.org/installing.html
```

### 安装LaTeX引擎（用于高质量PDF）
```bash
# Windows
choco install miktex

# macOS
brew install --cask mactex

# Ubuntu/Debian
sudo apt-get install texlive-latex-recommended texlive-fonts-recommended
```

### 转换命令
```bash
# 基础转换
pandoc AI_Engineer_90Day_Master_Plan.md -o AI_Engineer_90Day_Master_Plan.pdf

# 高质量转换（推荐）
pandoc AI_Engineer_90Day_Master_Plan.md \
  --pdf-engine=xelatex \
  --template=eisvogel \
  --listings \
  -V colorlinks=true \
  -V linkcolor=blue \
  -V urlcolor=blue \
  -V toccolor=gray \
  --toc \
  --toc-depth=3 \
  -V geometry:margin=2cm \
  -V fontsize=11pt \
  -V papersize=a4 \
  -o AI_Engineer_90Day_Master_Plan.pdf
```

---

## 🔧 方法二：使用Python脚本

### 安装依赖
```bash
pip install markdown[extra] weasyprint
```

### Python转换脚本
创建文件 `convert_to_pdf.py`：
```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import markdown
from weasyprint import HTML, CSS
from weasyprint.fonts import FontConfiguration
import os

def convert_md_to_pdf(md_file, pdf_file):
    """将Markdown文件转换为PDF"""
    
    # 读取Markdown文件
    with open(md_file, 'r', encoding='utf-8') as f:
        md_content = f.read()
    
    # 转换为HTML
    md = markdown.Markdown(extensions=[
        'extra',
        'codehilite',
        'toc',
        'tables'
    ])
    html_content = md.convert(md_content)
    
    # 创建完整的HTML文档
    full_html = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>AI工程师90天学习计划</title>
        <style>
            body {{
                font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 210mm;
                margin: 0 auto;
                padding: 20px;
            }}
            h1, h2, h3, h4, h5, h6 {{
                color: #2c3e50;
                margin-top: 2em;
                margin-bottom: 1em;
            }}
            h1 {{
                border-bottom: 3px solid #3498db;
                padding-bottom: 10px;
            }}
            h2 {{
                border-bottom: 2px solid #e74c3c;
                padding-bottom: 8px;
            }}
            table {{
                border-collapse: collapse;
                width: 100%;
                margin: 1em 0;
                font-size: 0.9em;
            }}
            th, td {{
                border: 1px solid #ddd;
                padding: 8px;
                text-align: left;
            }}
            th {{
                background-color: #3498db;
                color: white;
                font-weight: bold;
            }}
            tr:nth-child(even) {{
                background-color: #f2f2f2;
            }}
            code {{
                background-color: #f8f8f8;
                border: 1px solid #e1e1e8;
                border-radius: 3px;
                padding: 2px 4px;
                font-family: 'Consolas', 'Monaco', monospace;
            }}
            pre {{
                background-color: #f8f8f8;
                border: 1px solid #e1e1e8;
                border-radius: 5px;
                padding: 15px;
                overflow-x: auto;
            }}
            blockquote {{
                border-left: 4px solid #3498db;
                margin: 1em 0;
                padding: 0.5em 1em;
                background-color: #f9f9f9;
            }}
            .emoji {{
                font-size: 1.2em;
            }}
            @page {{
                size: A4;
                margin: 2cm;
            }}
        </style>
    </head>
    <body>
        {html_content}
    </body>
    </html>
    """
    
    # 转换为PDF
    font_config = FontConfiguration()
    HTML(string=full_html).write_pdf(
        pdf_file,
        font_config=font_config
    )
    
    print(f"✅ PDF转换完成：{pdf_file}")

if __name__ == "__main__":
    convert_md_to_pdf(
        "AI_Engineer_90Day_Master_Plan.md",
        "AI_Engineer_90Day_Master_Plan.pdf"
    )
```

### 运行转换
```bash
python convert_to_pdf.py
```

---

## 🔧 方法三：使用在线工具

### 推荐的在线转换器
1. **Markdown to PDF Online**
   - 网址：https://www.markdowntopdf.com/
   - 优点：简单易用，支持中文
   - 使用：上传MD文件，点击转换

2. **Pandoc Try**
   - 网址：https://pandoc.org/try/
   - 优点：支持多种格式转换
   - 使用：粘贴内容，选择输出格式

3. **Dillinger.io**
   - 网址：https://dillinger.io/
   - 优点：实时预览，导出PDF
   - 使用：粘贴内容，Export as PDF

---

## 🔧 方法四：使用VS Code插件

### 安装插件
1. 打开VS Code
2. 搜索并安装 "Markdown PDF" 插件
3. 安装 "Markdown All in One" 插件（可选，增强功能）

### 转换步骤
1. 在VS Code中打开 `AI_Engineer_90Day_Master_Plan.md`
2. 按 `Ctrl+Shift+P` 打开命令面板
3. 输入 "Markdown PDF: Export (pdf)"
4. 选择保存位置

### 配置优化
在VS Code设置中添加：
```json
{
    "markdown-pdf.format": "A4",
    "markdown-pdf.displayHeaderFooter": true,
    "markdown-pdf.headerTemplate": "<div style='font-size:9px; margin-left:1cm;'><span class='title'></span></div>",
    "markdown-pdf.footerTemplate": "<div style='font-size:9px; margin:0 1cm;'><span class='date'></span> <span style='float:right;'><span class='pageNumber'></span>/<span class='totalPages'></span></span></div>",
    "markdown-pdf.margin.top": "2cm",
    "markdown-pdf.margin.bottom": "2cm",
    "markdown-pdf.margin.right": "1cm",
    "markdown-pdf.margin.left": "1cm"
}
```

---

## 🔧 方法五：使用Typora（最简单）

### 步骤
1. 下载并安装Typora：https://typora.io/
2. 用Typora打开 `AI_Engineer_90Day_Master_Plan.md`
3. 点击 `文件` → `导出` → `PDF`
4. 选择保存位置

### 优点
- 界面友好，所见即所得
- 支持中文字体
- 表格和图表渲染效果好
- 不需要命令行操作

---

## 📊 各方法对比

| 方法 | 难度 | 效果质量 | 中文支持 | 表格支持 | 推荐度 |
|------|------|----------|----------|----------|--------|
| Pandoc | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| Python脚本 | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ |
| 在线工具 | ⭐ | ⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐ |
| VS Code插件 | ⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ |
| Typora | ⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |

---

## 🎯 推荐方案

### 对于普通用户
**推荐使用Typora**：最简单，效果最好，支持中文完美

### 对于开发者
**推荐使用Pandoc**：命令行工具，可自动化，输出质量高

### 快速方案
**使用VS Code插件**：如果已经在用VS Code，直接安装插件最方便

---

## 📋 转换后检查清单

转换完成后，请检查PDF文档是否包含：
- [ ] 完整的目录结构
- [ ] 所有表格正确显示
- [ ] 中文字符正常显示
- [ ] 代码块格式正确
- [ ] 链接可以点击（如果需要）
- [ ] 页码和页眉页脚
- [ ] 合适的字体大小和行距

---

## 🚀 立即行动

选择最适合您的方法，现在就开始转换：

1. **最快速**：使用Typora，5分钟搞定
2. **最专业**：使用Pandoc，10分钟配置，终身受用
3. **最方便**：VS Code插件，2分钟安装，1分钟转换

您的90天AI学习计划即将变成一份精美的PDF手册！