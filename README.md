# 🔥 凤凰涅槃计划V3：终极AI开发环境

支持**CUDA GPU计算**、**Go微服务**、**C++高性能**、**Python AI/ML**的全栈开发环境，专为中国大陆网络环境优化。

## 🎯 项目目标

根据凤凰涅槃计划V3，本环境支持以下三个核心项目的开发：

1. **`libcuda_ops`** - 高性能CUDA算子库 (C++/CUDA)
2. **`FusedTransformer`** - 融合型Transformer模块 (Python/C++)
3. **`Inferno`** - 多后端推理服务平台 (Go)

## ✨ 主要特性

### 🐍 Python AI/ML生态
- **Python 3.10**: AI/ML主力语言
- **📊 数据科学**: NumPy, Pandas, Scikit-learn
- **🎨 可视化**: Matplotlib, Seaborn, Plotly
- **🚀 Web应用**: Streamlit, Gradio

### 🤖 AI/ML框架
- **PyTorch 2.1.2**: 深度学习框架 + CUDA 12.1支持
- **Transformers**: HuggingFace生态 + Accelerate + Datasets
- **vLLM 0.2.6**: 高性能大模型推理引擎
- **TensorFlow**: 深度学习框架 + TensorBoard
- **LangChain**: 大模型应用框架
- **LlamaIndex**: 知识库构建

### ⚡ CUDA GPU计算
- **NVIDIA CUDA 12.1.1** + **cuDNN 8**
- **Tensor Core** 支持 (FP16/BF16)
- **CUTLASS** 高性能GEMM库
- **Nsight Systems/Compute** 性能分析
- **WMMA API** 混合精度计算

### 🔧 C++高性能开发
- **GCC/Clang** 现代C++编译器
- **CMake** + **Ninja** 构建系统
- **Google Test** 单元测试框架
- **Google Benchmark** 性能基准测试
- **pybind11** Python绑定

### 🐹 Go微服务开发
- **Go 1.21.5** 最新稳定版
- **Gin** Web框架
- **gRPC** + **Protocol Buffers**
- **Delve** 调试器
- **golangci-lint** 代码检查

### 🛠️ 开发工具
- **Jupyter Lab**: 交互式开发环境
- **VS Code Web**: 浏览器IDE
- **代码质量**: Black, isort, flake8, mypy
- **性能监控**: GPU状态监控和分析
- **TensorRT**: 推理优化
- **ONNX**: 模型交换格式

## 📁 目录结构

```
/workspace/
├── models/          # AI模型存储
│   ├── huggingface/ # HuggingFace模型缓存
│   ├── gguf/        # GGUF格式模型
│   ├── onnx/        # ONNX模型
│   └── tensorrt/    # TensorRT引擎
├── datasets/        # 训练数据集
├── checkpoints/     # 训练检查点
├── configs/         # 配置文件
├── scripts/         # 实用脚本
├── logs/            # 运行日志
├── templates/       # 项目模板
│   ├── python/      # Python AI/ML模板
│   ├── cpp/         # C++高性能模板
│   ├── go/          # Go微服务模板
│   ├── cuda/        # CUDA算子模板
│   └── rust/        # Rust系统模板
├── examples/        # 示例代码
│   ├── ai-ml/       # AI/ML示例
│   ├── cuda-ops/    # CUDA算子示例
│   ├── go-services/ # Go微服务示例
│   ├── cpp-performance/ # C++性能示例
│   └── rust-systems/    # Rust系统示例
├── projects/        # 核心项目
│   ├── libcuda_ops/     # 高性能CUDA算子库
│   ├── fused_transformer/ # 融合型Transformer模块
│   └── inferno_service/   # Go多后端推理服务
└── go/              # Go工作空间
    ├── bin/         # Go二进制文件
    ├── src/         # Go源代码
    └── pkg/         # Go包缓存
├── examples/        # Python示例
│   └── ai-ml/       # AI/ML示例
└── projects/        # 开发项目
```

## 🚀 快速开始

### 前置要求
- Docker 20.10+
- Docker Compose 2.0+
- NVIDIA Docker Runtime
- NVIDIA GPU驱动 (支持CUDA 12.1+)

### 1. 环境初始化
```bash
# 克隆项目
git clone <repository-url>
cd <project-directory>

# 运行初始化脚本
chmod +x setup-environment.sh
./setup-environment.sh
```

### 2. 启动环境
```bash
# 使用Docker Compose
docker-compose up -d

# 或使用Makefile
make start
```

### 3. 访问服务
- **Jupyter Lab**: http://localhost:18888
- **TensorBoard**: http://localhost:16006
- **vLLM API**: http://localhost:18000

### 4. 进入开发环境
```bash
# 进入容器
docker-compose exec ai-dev zsh

# 或使用Makefile
make enter
```

## 💻 Python开发

### AI/ML开发
```bash
cd /workspace/templates/python
python -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

### 运行示例
```bash
python /workspace/examples/ai-ml/pytorch_example.py
```

### 启动Jupyter Lab
```bash
jupyter lab --allow-root
```

### 启动vLLM推理服务
```bash
start-vllm
```

### 下载HuggingFace模型
```bash
download-hf-models
```

## 🔧 管理命令

### 使用Makefile
```bash
make help      # 显示帮助信息
make setup     # 初始化环境
make build     # 构建镜像
make start     # 启动服务
make stop      # 停止服务
make restart   # 重启服务
make enter     # 进入容器
make logs      # 查看日志
make clean     # 清理环境
make status    # 查看状态
```

### 使用Docker Compose
```bash
docker-compose up -d          # 后台启动
docker-compose down           # 停止服务
docker-compose logs -f        # 查看日志
docker-compose exec ai-dev zsh  # 进入环境
```

## 📊 性能监控

### GPU状态监控
```bash
# 实时监控
gpu-watch

# 单次查看
gpu-status
```

### 系统资源监控
```bash
# 容器资源使用
docker stats

# GPU详细信息
nvidia-smi
```

## 🔧 自定义配置

### 环境变量
编辑 `.env` 文件来自定义配置：
```bash
PROJECT_NAME=python-ai-dev
COMPOSE_PROJECT_NAME=ai-dev
```

### 端口映射
修改 `docker-compose.yml` 中的端口映射：
```yaml
ports:
  - "18888:8888"   # Jupyter Lab
  - "16006:6006"   # TensorBoard
  - "18000:8000"   # vLLM API
```

## 🐛 故障排除

### 常见问题

1. **GPU不可用**
   ```bash
   # 检查NVIDIA驱动
   nvidia-smi
   
   # 检查Docker GPU支持
   docker run --rm --gpus all nvidia/cuda:12.1.1-base-ubuntu22.04 nvidia-smi
   ```

2. **端口冲突**
   ```bash
   # 检查端口占用
   netstat -tulpn | grep :8888
   
   # 修改docker-compose.yml中的端口映射
   ```

3. **内存不足**
   ```bash
   # 检查系统内存
   free -h
   
   # 检查Docker内存限制
   docker system df
   ```

### 日志查看
```bash
# 查看容器日志
docker-compose logs ai-dev

# 查看构建日志
docker-compose build --no-cache ai-dev
```

## 📝 更新日志

### v2.0.0 (精简版)
- ✅ 移除多语言支持 (Rust, Go, Node.js, TypeScript, C++)
- ✅ 专注Python AI/ML开发
- ✅ 减少Docker镜像大小
- ✅ 简化配置和维护
- ✅ 保留所有核心AI/ML功能

### v1.0.0 (完整版)
- 🔧 多语言开发环境支持
- 🤖 完整AI/ML框架集成
- 🐳 Docker容器化部署
- 🇨🇳 中国大陆环境优化

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📞 支持

如有问题，请提交Issue或联系维护者。
