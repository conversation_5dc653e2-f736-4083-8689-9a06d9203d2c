# 🪟 凤凰涅槃计划V4 - Windows环境设置指南

## 📋 概述

本指南将帮助您在Windows系统上成功部署和运行凤凰涅槃计划V4企业级AI开发环境。我们提供了多种Windows兼容的启动方式，确保在不同Windows环境下都能正常工作。

## 🔧 系统要求

### 最低系统要求
- **操作系统**: Windows 10 版本 2004 或更高版本 / Windows 11
- **内存**: 16GB RAM (推荐 32GB)
- **存储**: 50GB 可用磁盘空间
- **GPU**: NVIDIA RTX 4070s 或同等级别 (可选，但强烈推荐)

### 必需软件
1. **Docker Desktop for Windows**
   - 版本: 4.0 或更高
   - 下载地址: https://www.docker.com/products/docker-desktop
   - 确保启用 WSL 2 后端

2. **NVIDIA Container Toolkit** (GPU支持)
   - 仅在使用NVIDIA GPU时需要
   - 下载地址: https://docs.nvidia.com/datacenter/cloud-native/container-toolkit/install-guide.html

## 🚀 快速开始

### 方法1: PowerShell脚本 (推荐)

适用于 Windows PowerShell 5.1+ 和 PowerShell Core 7+

```powershell
# 1. 以管理员身份打开PowerShell
# 2. 设置执行策略 (如果需要)
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

# 3. 启动V4环境
.\start_v4_environment.ps1 start
```

### 方法2: 批处理文件

适用于传统的Windows命令提示符

```cmd
# 1. 以管理员身份打开命令提示符
# 2. 启动V4环境
start_v4_environment.bat start
```

### 方法3: WSL (Windows Subsystem for Linux)

如果您安装了WSL，可以使用原始的bash脚本

```bash
# 在WSL终端中执行
./start_v4_environment.sh start
```

## 📁 文件结构

确保您的项目目录包含以下文件：

```
phoenix-v4/
├── Dockerfile.robust                 # V4 Dockerfile
├── docker-compose.v4.yml            # Docker Compose配置
├── start_v4_environment.ps1         # PowerShell启动脚本
├── start_v4_environment.bat         # 批处理启动脚本
├── start_v4_environment.sh          # Bash启动脚本 (WSL)
├── verify_v4_environment.py         # 环境验证脚本
├── workspace/                       # 项目工作目录
├── data/                            # 数据目录
├── models/                          # 模型目录
├── mlruns/                          # MLflow运行记录
├── logs/                            # 日志目录
└── monitoring/                      # 监控配置目录
```

## 🔧 详细使用说明

### PowerShell脚本命令

```powershell
# 构建并启动完整环境
.\start_v4_environment.ps1 start

# 仅构建镜像
.\start_v4_environment.ps1 build

# 停止所有服务
.\start_v4_environment.ps1 stop

# 重启服务
.\start_v4_environment.ps1 restart

# 查看服务状态
.\start_v4_environment.ps1 status

# 查看服务日志
.\start_v4_environment.ps1 logs

# 清理所有数据和镜像
.\start_v4_environment.ps1 clean
```

### 批处理文件命令

```cmd
REM 构建并启动完整环境
start_v4_environment.bat start

REM 仅构建镜像
start_v4_environment.bat build

REM 停止所有服务
start_v4_environment.bat stop

REM 重启服务
start_v4_environment.bat restart

REM 查看服务状态
start_v4_environment.bat status

REM 查看服务日志
start_v4_environment.bat logs

REM 清理所有数据和镜像
start_v4_environment.bat clean
```

## 🌐 访问地址

启动成功后，您可以通过以下地址访问各种服务：

| 服务 | 地址 | 用户名/密码 |
|------|------|-------------|
| **Jupyter Lab** | http://localhost:8888 | 无需认证 |
| **MLflow UI** | http://localhost:5000 | 无需认证 |
| **Grafana** | http://localhost:3000 | admin/phoenix_v4_2024 |
| **Prometheus** | http://localhost:9090 | 无需认证 |
| **Jaeger UI** | http://localhost:16686 | 无需认证 |
| **Neo4j Browser** | http://localhost:7474 | phoenix/phoenix_v4_2024 |

## 🗄️ 数据库连接信息

| 数据库 | 地址 | 端口 | 用户名/密码 |
|--------|------|------|-------------|
| **PostgreSQL** | localhost | 5432 | phoenix/phoenix_v4_2024 |
| **Redis** | localhost | 6379 | 无密码 |
| **ClickHouse** | localhost | 9000 | phoenix/phoenix_v4_2024 |
| **Neo4j** | localhost | 7687 | phoenix/phoenix_v4_2024 |
| **Kafka** | localhost | 9092 | 无认证 |

## 🔍 故障排除

### 常见问题及解决方案

#### 1. Docker Desktop未启动
**错误**: `docker: command not found` 或 `Docker Desktop is not running`

**解决方案**:
- 启动Docker Desktop应用程序
- 等待Docker完全启动（系统托盘图标变为绿色）
- 重新运行启动脚本

#### 2. PowerShell执行策略限制
**错误**: `execution of scripts is disabled on this system`

**解决方案**:
```powershell
# 以管理员身份运行PowerShell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

#### 3. 端口冲突
**错误**: `port is already allocated`

**解决方案**:
- 检查是否有其他服务占用相同端口
- 停止冲突的服务或修改docker-compose.v4.yml中的端口映射

#### 4. 内存不足
**错误**: 构建过程中出现内存错误

**解决方案**:
- 在Docker Desktop设置中增加内存分配（推荐8GB+）
- 关闭其他占用内存的应用程序

#### 5. GPU支持问题
**错误**: CUDA不可用或GPU未检测到

**解决方案**:
- 确保安装了NVIDIA Container Toolkit
- 重启Docker Desktop
- 检查NVIDIA驱动程序是否最新

### 日志查看

```powershell
# 查看所有服务日志
.\start_v4_environment.ps1 logs

# 查看特定服务日志
docker-compose -f docker-compose.v4.yml logs phoenix-v4

# 实时查看日志
docker-compose -f docker-compose.v4.yml logs -f phoenix-v4
```

### 环境验证

```powershell
# 运行环境验证脚本
docker exec phoenix-v4-main python /workspace/project/verify_v4_environment.py

# 手动检查GPU状态
docker exec phoenix-v4-main nvidia-smi

# 检查Python环境
docker exec -it phoenix-v4-main bash
source /opt/miniconda/bin/activate ai
python -c "import torch, jax, mlflow; print('All frameworks loaded successfully!')"
```

## 🎯 Windows特定优化

### 1. 路径处理
- 使用绝对路径挂载卷以避免路径解析问题
- 支持Windows风格的路径分隔符

### 2. 性能优化
```powershell
# 为Docker Desktop分配更多资源
# 设置 -> Resources -> Advanced
# CPU: 4-8 cores
# Memory: 8-16 GB
# Swap: 2 GB
```

### 3. 网络配置
- 确保Windows防火墙允许Docker Desktop
- 如果使用企业网络，可能需要配置代理

## 📚 进阶使用

### 自定义配置

1. **修改端口映射**:
   编辑 `docker-compose.v4.yml` 文件中的 `ports` 部分

2. **调整资源限制**:
   在 `docker-compose.v4.yml` 中添加资源限制配置

3. **持久化数据**:
   所有重要数据都存储在命名卷中，重启容器不会丢失数据

### 开发工作流

```powershell
# 1. 启动开发环境
.\start_v4_environment.ps1 start

# 2. 进入开发容器
docker exec -it phoenix-v4-main bash

# 3. 激活AI环境
source /opt/miniconda/bin/activate ai

# 4. 开始开发
cd /workspace/project
jupyter lab --ip=0.0.0.0 --port=8888 --allow-root
```

## 🆘 获取帮助

如果遇到问题，请按以下顺序尝试：

1. **查看日志**: `.\start_v4_environment.ps1 logs`
2. **检查服务状态**: `.\start_v4_environment.ps1 status`
3. **重启服务**: `.\start_v4_environment.ps1 restart`
4. **清理重建**: `.\start_v4_environment.ps1 clean` 然后 `.\start_v4_environment.ps1 start`

## 🎉 成功启动标志

当您看到以下信息时，表示V4环境已成功启动：

```
🚀 凤凰涅槃计划V4企业级AI开发环境已启动！

📊 Web界面访问地址:
  • Jupyter Lab:    http://localhost:8888
  • MLflow UI:      http://localhost:5000
  • Grafana:        http://localhost:3000
  ...
```

现在您可以开始使用企业级AI开发环境进行项目开发了！🎯
