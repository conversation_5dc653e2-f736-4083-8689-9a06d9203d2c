#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const os = require('os');

/**
 * 安全地删除文件或目录，如果存在的话。
 * @param {string} pathToDel 要删除的路径
 */
function deletePath(pathToDel) {
  if (fs.existsSync(pathToDel)) {
    console.log(`  - 正在删除: ${pathToDel}`);
    try {
      // fs.rmSync 是 Node.js v14.14.0+ 的功能，能递归删除
      fs.rmSync(pathToDel, { recursive: true, force: true });
      console.log(`    > 成功删除。`);
    } catch (err) {
      console.error(`    > 删除失败: ${err.message}`);
    }
  } else {
    console.log(`  - 未找到: ${pathToDel} (跳过)`);
  }
}

function run() {
  console.log("开始清除 Augment 插件的登录信息 (跨平台版)...");

  const homeDir = os.homedir();
  const platform = process.platform;

  // 1. 删除用户主目录下的 .augment 文件夹
  const augmentConfigDir = path.join(homeDir, '.augment');
  deletePath(augmentConfigDir);
  
  // 2. 删除工作区本地的 .augment 文件夹
  const localAugmentDir = path.join(process.cwd(), '.augment');
  deletePath(localAugmentDir);

  // 3. 根据操作系统，删除 VSCode globalStorage 中的 Augment 相关数据
  let vscodeStorageDir;

  if (platform === 'win32') { // Windows
    const appData = process.env.APPDATA;
    if (appData) {
      vscodeStorageDir = path.join(appData, 'Code', 'User', 'globalStorage');
    }
  } else if (platform === 'darwin') { // macOS
    vscodeStorageDir = path.join(homeDir, 'Library', 'Application Support', 'Code', 'User', 'globalStorage');
  } else if (platform === 'linux') { // Linux
    vscodeStorageDir = path.join(homeDir, '.config', 'Code', 'User', 'globalStorage');
  }

  if (vscodeStorageDir && fs.existsSync(vscodeStorageDir)) {
    console.log(`正在 VSCode globalStorage (${vscodeStorageDir}) 中搜索 Augment 数据...`);
    let found = false;
    try {
      const entries = fs.readdirSync(vscodeStorageDir);
      for (const entry of entries) {
        // 插件的存储目录通常是 publisher.extensionName
        if (entry.toLowerCase().includes('augment')) {
          const fullPath = path.join(vscodeStorageDir, entry);
          deletePath(fullPath);
          found = true;
        }
      }
      if (!found) {
        console.log("  - 在 globalStorage 中未找到 'augment' 相关目录。");
      }
    } catch (err) {
        console.error(`  > 读取 globalStorage 目录失败: ${err.message}`);
    }
  } else {
    console.log(`  - 未找到 VSCode globalStorage 目录 (跳过)`);
  }

  console.log("\n文件清理完成！");
  console.log("\n下一步：请按以下步骤彻底清除登录凭据（关键步骤）");
  console.log("======================================================");
  console.log("插件的登录信息很可能存储在 VSCode 的安全存储中，普通脚本无法访问。");
  console.log("请按照以下两个步骤操作：\n");
  
  console.log("步骤 1：清除 VSCode 中的插件密钥 (最关键)");
  console.log("-----------------------------------------");
  console.log("  a. 在 VSCode 中，按下快捷键 `Ctrl+Shift+P` (Windows/Linux) 或 `Cmd+Shift+P` (macOS) 打开命令面板。");
  console.log("  b. 在命令面板中，输入 `>Secrets: Clear` 或中文 `>机密: 清除`。");
  console.log("  c. 在出现的列表中，找到并选择包含 `Augment` 字样的条目 (例如 `Augment.auth` 或类似名称)。");
  console.log("  d. 按下回车确认。这将从操作系统的密钥链中删除该插件的认证令牌。\n");

  console.log("步骤 2：清除浏览器 Cookies");
  console.log("-----------------------------------------");
  console.log("  a. 打开你的网络浏览器，进入“设置”。");
  console.log("  b. 找到“隐私和安全” -> “Cookie 和其他网站数据”。");
  console.log("  c. 找到并点击“查看所有网站数据和权限”。");
  console.log("  d. 搜索 `augmentcode.com` 并删除所有相关条目。\n");

  console.log("======================================================");
  console.log("完成以上两个步骤并重启 VSCode 后，Augment 插件的登录信息才会被彻底清除。");
}

run();