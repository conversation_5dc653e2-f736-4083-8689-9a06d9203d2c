#!/bin/bash

# 这是一个用于清除 VSCode Augment 插件登录信息的脚本。
# 它会删除插件在标准位置存储的配置文件和缓存。

echo "开始清除 Augment 插件的登录信息..."

# 函数：删除并报告
delete_path() {
    local path_to_delete=$1
    if [ -e "$path_to_delete" ]; then
        echo "  - 正在删除: $path_to_delete"
        rm -rf "$path_to_delete"
        if [ $? -eq 0 ]; then
            echo "    > 成功删除。"
        else
            echo "    > 删除失败，请检查权限。"
        fi
    else
        echo "  - 未找到: $path_to_delete (跳过)"
    fi
}

# 1. 删除用户主目录下的 .augment 文件夹
AUGMENT_CONFIG_DIR="$HOME/.augment"
delete_path "$AUGMENT_CONFIG_DIR"

# 2. 删除 VSCode globalStorage 中的 Augment 相关数据
#    插件的 full publisher name 未知，我们将搜索包含 "augment" 的目录。
#    这可以覆盖类似 "publisher.augment" 这样的文件夹。

VSCODE_STORAGE_DIR=""
OS=$(uname)

if [ "$OS" == "Linux" ]; then
    VSCODE_STORAGE_DIR="$HOME/.config/Code/User/globalStorage"
elif [ "$OS" == "Darwin" ]; then # macOS
    VSCODE_STORAGE_DIR="$HOME/Library/Application Support/Code/User/globalStorage"
else
    echo "不支持的操作系统: $OS"
    echo "请在 Windows 上手动删除以下目录中的 'augment' 相关文件夹:"
    echo "  - %USERPROFILE%\\.augment"
    echo "  - %APPDATA%\\Code\\User\\globalStorage"
    exit 1
fi

echo "正在 VSCode globalStorage 中搜索 Augment 数据..."
if [ -d "$VSCODE_STORAGE_DIR" ]; then
    found=false
    for dir in "$VSCODE_STORAGE_DIR"/*augment*/; do
        if [ -d "$dir" ]; then
            found=true
            delete_path "$dir"
        fi
    done
    if [ "$found" = false ]; then
        echo "  - 在 globalStorage 中未找到 'augment' 相关目录。"
    fi
else
    echo "  - 未找到 VSCode globalStorage 目录: $VSCODE_STORAGE_DIR (跳过)"
fi

echo "清理完成！请重启 VSCode 使更改生效。"