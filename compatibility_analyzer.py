#!/usr/bin/env python3
"""
凤凰涅槃计划V4 - 兼容性分析工具
检查Dockerfile.robust中所有组件与基础镜像的兼容性
"""

import re
import json
from typing import Dict, List, Tuple, Any
from dataclasses import dataclass
from enum import Enum

class CompatibilityLevel(Enum):
    COMPATIBLE = "✅ 兼容"
    WARNING = "⚠️ 警告"
    INCOMPATIBLE = "❌ 不兼容"
    UNKNOWN = "❓ 未知"

@dataclass
class ComponentInfo:
    name: str
    version: str
    category: str
    compatibility: CompatibilityLevel
    issues: List[str]
    recommendations: List[str]

class CompatibilityAnalyzer:
    def __init__(self):
        self.base_image = "nvidia/cuda:12.2.2-cudnn8-devel-ubuntu22.04"
        self.cuda_version = "12.2.2"
        self.cudnn_version = "8"
        self.ubuntu_version = "22.04"
        self.python_version = "3.10"
        
        # 兼容性数据库
        self.compatibility_db = self._load_compatibility_db()
        
    def _load_compatibility_db(self) -> Dict[str, Any]:
        """加载兼容性数据库"""
        return {
            "cuda_compatibility": {
                "pytorch": {
                    "2.1.2": {"cuda_min": "11.8", "cuda_max": "12.1", "status": "warning"},
                    "2.2.0": {"cuda_min": "11.8", "cuda_max": "12.1", "status": "compatible"},
                },
                "jax": {
                    "0.4.20": {"cuda_min": "11.4", "cuda_max": "12.3", "status": "compatible"},
                },
                "tensorflow": {
                    "2.13": {"cuda_min": "11.8", "cuda_max": "12.2", "status": "compatible"},
                    "2.14": {"cuda_min": "11.8", "cuda_max": "12.3", "status": "compatible"},
                }
            },
            "ubuntu_packages": {
                "22.04": {
                    "available": [
                        "postgresql-14", "postgresql-client", "redis-server", 
                        "openjdk-11-jdk-headless", "gcc-11", "g++-11", "clang-14",
                        "docker.io", "docker-compose"
                    ],
                    "unavailable": [
                        "postgresql-15", "postgresql-client-15"  # 需要官方源
                    ]
                }
            },
            "python_compatibility": {
                "3.10": {
                    "compatible": [
                        "torch==2.1.2", "jax==0.4.20", "transformers==4.36.0",
                        "mlflow==2.8.1", "apache-airflow==2.7.3"
                    ]
                }
            }
        }
    
    def analyze_dockerfile(self, dockerfile_path: str) -> Dict[str, Any]:
        """分析Dockerfile的兼容性"""
        with open(dockerfile_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        analysis_result = {
            "base_image_info": self._analyze_base_image(),
            "cuda_compatibility": self._analyze_cuda_compatibility(content),
            "ubuntu_packages": self._analyze_ubuntu_packages(content),
            "python_packages": self._analyze_python_packages(content),
            "system_dependencies": self._analyze_system_dependencies(content),
            "architecture_compatibility": self._analyze_architecture_compatibility(),
            "overall_assessment": None,
            "recommendations": []
        }
        
        # 生成总体评估
        analysis_result["overall_assessment"] = self._generate_overall_assessment(analysis_result)
        analysis_result["recommendations"] = self._generate_recommendations(analysis_result)
        
        return analysis_result
    
    def _analyze_base_image(self) -> Dict[str, Any]:
        """分析基础镜像信息"""
        return {
            "image": self.base_image,
            "cuda_version": self.cuda_version,
            "cudnn_version": self.cudnn_version,
            "ubuntu_version": self.ubuntu_version,
            "architecture": "x86_64",
            "gpu_support": "RTX 4070s (Ada Lovelace)",
            "compatibility_notes": [
                "CUDA 12.2.2 支持 RTX 4070s",
                "cuDNN 8 与 CUDA 12.2.2 兼容",
                "Ubuntu 22.04 LTS 长期支持版本"
            ]
        }
    
    def _analyze_cuda_compatibility(self, content: str) -> Dict[str, Any]:
        """分析CUDA相关组件兼容性"""
        issues = []
        warnings = []
        compatible_components = []
        
        # 检查PyTorch版本
        pytorch_match = re.search(r'PYTORCH_VERSION=([0-9.]+)', content)
        if pytorch_match:
            pytorch_version = pytorch_match.group(1)
            if pytorch_version in self.compatibility_db["cuda_compatibility"]["pytorch"]:
                compat_info = self.compatibility_db["cuda_compatibility"]["pytorch"][pytorch_version]
                if compat_info["status"] == "warning":
                    warnings.append(f"PyTorch {pytorch_version} 对 CUDA 12.2.2 支持有限，建议升级到 2.2.0+")
                else:
                    compatible_components.append(f"PyTorch {pytorch_version}")
        
        # 检查JAX版本
        jax_match = re.search(r'JAX_VERSION=([0-9.]+)', content)
        if jax_match:
            jax_version = jax_match.group(1)
            compatible_components.append(f"JAX {jax_version}")
        
        # 检查CUDA库版本
        if "libcublas-dev-12-2" in content:
            compatible_components.append("cuBLAS 12.2")
        if "libcurand-dev-12-2" in content:
            compatible_components.append("cuRAND 12.2")
        
        return {
            "compatible_components": compatible_components,
            "warnings": warnings,
            "issues": issues,
            "cuda_version_used": self.cuda_version,
            "recommendations": [
                "考虑升级 PyTorch 到 2.2.0+ 以获得更好的 CUDA 12.2.2 支持",
                "验证 TensorRT 与 CUDA 12.2.2 的兼容性"
            ]
        }
    
    def _analyze_ubuntu_packages(self, content: str) -> Dict[str, Any]:
        """分析Ubuntu包兼容性"""
        issues = []
        warnings = []
        compatible_packages = []
        
        # 检查PostgreSQL
        if "postgresql-client-${POSTGRESQL_VERSION}" in content:
            issues.append("使用了版本特定的PostgreSQL客户端包名，在Ubuntu 22.04中可能不可用")
        elif "postgresql-client" in content:
            compatible_packages.append("PostgreSQL客户端 (通用版本)")
        
        # 检查Redis
        if "redis-server" in content:
            compatible_packages.append("Redis服务器")
        
        # 检查Java
        if "openjdk-11-jdk-headless" in content:
            compatible_packages.append("OpenJDK 11")
        
        # 检查编译器
        if "gcc-11" in content and "g++-11" in content:
            compatible_packages.append("GCC 11 编译器")
        
        # 检查Docker
        if "docker.io" in content:
            warnings.append("在容器内安装Docker可能导致嵌套容器问题")
        
        return {
            "compatible_packages": compatible_packages,
            "warnings": warnings,
            "issues": issues,
            "ubuntu_version": self.ubuntu_version,
            "package_sources": ["官方源", "PostgreSQL官方源", "ClickHouse官方源", "Neo4j官方源"]
        }
    
    def _analyze_python_packages(self, content: str) -> Dict[str, Any]:
        """分析Python包兼容性"""
        issues = []
        warnings = []
        compatible_packages = []
        
        # 检查Python版本
        if "python=3.10" in content:
            compatible_packages.append("Python 3.10")
        
        # 检查PyTorch CUDA版本
        if "--extra-index-url https://download.pytorch.org/whl/cu121" in content:
            warnings.append("PyTorch使用CUDA 12.1版本，与CUDA 12.2.2可能有兼容性问题")
        
        # 检查JAX CUDA版本
        if "jax[cuda12_pip]" in content:
            compatible_packages.append("JAX CUDA 12支持")
        
        # 检查其他包
        key_packages = [
            "transformers", "accelerate", "datasets", "tokenizers",
            "mlflow", "apache-airflow", "dvc", "faiss-gpu"
        ]
        
        for package in key_packages:
            if package in content:
                compatible_packages.append(package)
        
        return {
            "compatible_packages": compatible_packages,
            "warnings": warnings,
            "issues": issues,
            "python_version": self.python_version,
            "pip_sources": ["官方PyPI", "阿里云PyPI镜像", "PyTorch官方源"]
        }
    
    def _analyze_system_dependencies(self, content: str) -> Dict[str, Any]:
        """分析系统依赖冲突"""
        potential_conflicts = []
        dependency_chains = []
        
        # 检查可能的冲突
        if "docker.io" in content and "docker-compose" in content:
            potential_conflicts.append("容器内Docker安装可能与宿主机Docker冲突")
        
        # 检查依赖链
        dependency_chains.extend([
            "CUDA 12.2.2 → cuDNN 8 → PyTorch 2.1.2",
            "Ubuntu 22.04 → PostgreSQL官方源 → PostgreSQL 15",
            "Python 3.10 → Conda → AI/ML包生态系统"
        ])
        
        return {
            "potential_conflicts": potential_conflicts,
            "dependency_chains": dependency_chains,
            "critical_dependencies": [
                "NVIDIA驱动 >= 525.60.13",
                "Docker >= 20.10",
                "足够的磁盘空间 (>20GB)"
            ]
        }
    
    def _analyze_architecture_compatibility(self) -> Dict[str, Any]:
        """分析架构兼容性"""
        return {
            "target_architecture": "x86_64",
            "gpu_architecture": "Ada Lovelace (RTX 4070s)",
            "compute_capability": "8.9",
            "memory_requirements": "12GB VRAM, 32GB+ RAM推荐",
            "compatibility_notes": [
                "RTX 4070s 完全支持 CUDA 12.2.2",
                "Ada Lovelace架构支持所有现代AI框架",
                "12GB VRAM足够大多数AI/ML工作负载"
            ]
        }
    
    def _generate_overall_assessment(self, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """生成总体兼容性评估"""
        total_issues = 0
        total_warnings = 0
        
        for section in ["cuda_compatibility", "ubuntu_packages", "python_packages", "system_dependencies"]:
            if section in analysis:
                total_issues += len(analysis[section].get("issues", []))
                total_warnings += len(analysis[section].get("warnings", []))
        
        if total_issues == 0 and total_warnings == 0:
            level = CompatibilityLevel.COMPATIBLE
        elif total_issues == 0 and total_warnings <= 3:
            level = CompatibilityLevel.WARNING
        else:
            level = CompatibilityLevel.INCOMPATIBLE
        
        return {
            "compatibility_level": level.value,
            "total_issues": total_issues,
            "total_warnings": total_warnings,
            "build_success_probability": max(0, 100 - total_issues * 20 - total_warnings * 5),
            "summary": self._generate_summary(level, total_issues, total_warnings)
        }
    
    def _generate_summary(self, level: CompatibilityLevel, issues: int, warnings: int) -> str:
        """生成兼容性总结"""
        if level == CompatibilityLevel.COMPATIBLE:
            return "环境配置整体兼容，可以安全构建"
        elif level == CompatibilityLevel.WARNING:
            return f"环境配置基本兼容，但有{warnings}个警告需要注意"
        else:
            return f"环境配置存在{issues}个严重问题，需要修复后才能构建"
    
    def _generate_recommendations(self, analysis: Dict[str, Any]) -> List[str]:
        """生成修复建议"""
        recommendations = []
        
        # CUDA相关建议
        if analysis["cuda_compatibility"]["warnings"]:
            recommendations.extend([
                "升级PyTorch到2.2.0+以获得更好的CUDA 12.2.2支持",
                "验证所有CUDA相关包的版本兼容性"
            ])
        
        # Ubuntu包建议
        if analysis["ubuntu_packages"]["issues"]:
            recommendations.extend([
                "使用通用包名而非版本特定包名",
                "确保所有第三方源支持Ubuntu 22.04"
            ])
        
        # Python包建议
        if analysis["python_packages"]["warnings"]:
            recommendations.extend([
                "统一CUDA版本，使用cu122而非cu121",
                "验证所有Python包与Python 3.10的兼容性"
            ])
        
        return recommendations

def main():
    analyzer = CompatibilityAnalyzer()
    result = analyzer.analyze_dockerfile("Dockerfile.robust")
    
    # 输出分析结果
    print("🔍 凤凰涅槃计划V4 - 兼容性分析报告")
    print("=" * 60)
    
    # 基础镜像信息
    base_info = result["base_image_info"]
    print(f"\n📦 基础镜像: {base_info['image']}")
    print(f"   CUDA版本: {base_info['cuda_version']}")
    print(f"   Ubuntu版本: {base_info['ubuntu_version']}")
    print(f"   目标GPU: {base_info['gpu_support']}")
    
    # 总体评估
    assessment = result["overall_assessment"]
    print(f"\n🎯 总体兼容性: {assessment['compatibility_level']}")
    print(f"   构建成功率: {assessment['build_success_probability']}%")
    print(f"   问题数量: {assessment['total_issues']} 严重, {assessment['total_warnings']} 警告")
    print(f"   总结: {assessment['summary']}")
    
    # 详细分析
    sections = [
        ("CUDA兼容性", "cuda_compatibility"),
        ("Ubuntu包兼容性", "ubuntu_packages"),
        ("Python包兼容性", "python_packages"),
        ("系统依赖", "system_dependencies")
    ]
    
    for title, key in sections:
        if key in result:
            section = result[key]
            print(f"\n📋 {title}:")
            
            if "compatible_components" in section:
                for comp in section["compatible_components"]:
                    print(f"   ✅ {comp}")
            
            if "compatible_packages" in section:
                for pkg in section["compatible_packages"]:
                    print(f"   ✅ {pkg}")
            
            if "warnings" in section:
                for warning in section["warnings"]:
                    print(f"   ⚠️ {warning}")
            
            if "issues" in section:
                for issue in section["issues"]:
                    print(f"   ❌ {issue}")
    
    # 修复建议
    if result["recommendations"]:
        print(f"\n🔧 修复建议:")
        for i, rec in enumerate(result["recommendations"], 1):
            print(f"   {i}. {rec}")
    
    # 保存详细报告
    with open("compatibility_analysis_report.json", "w", encoding="utf-8") as f:
        json.dump(result, f, ensure_ascii=False, indent=2)
    
    print(f"\n📄 详细报告已保存到: compatibility_analysis_report.json")

if __name__ == "__main__":
    main()
