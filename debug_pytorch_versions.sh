#!/bin/bash

# PyTorch版本冲突诊断脚本
echo "🔍 PyTorch版本冲突诊断"
echo "======================="

echo "1. 检查当前环境变量配置："
echo "   PYTORCH_VERSION: 2.5.0"
echo "   TORCHVISION_VERSION: 0.20.0"
echo "   TORCHAUDIO_VERSION: 2.5.0"

echo ""
echo "2. 检查PyTorch 2.5.0兼容的torchvision版本："
echo "   正在查询官方兼容性..."

# 模拟pip依赖解析
echo ""
echo "3. 模拟当前安装命令的依赖解析："
echo "   pip install torch==2.5.0 torchvision torchaudio"
echo "   ❌ torchvision没有指定版本，pip会选择最新版本"
echo "   ❌ 最新torchvision需要torch>=2.6.0或2.7.0"

echo ""
echo "4. 正确的安装命令应该是："
echo "   pip install torch==2.5.0 torchvision==0.20.0 torchaudio==2.5.0"

echo ""
echo "5. 问题定位："
echo "   📍 Dockerfile.robust 第103-107行"
echo "   📍 install_pytorch_smart脚本没有使用版本变量"

echo ""
echo "6. 推荐解决方案："
echo "   ✅ 修复安装脚本，使用指定的版本变量"
echo "   ✅ 验证PyTorch 2.5.0 + torchvision 0.20.0兼容性"