# =============================================================================
# PyTorch版本冲突解决方案 - Docker Compose配置
# 提供多种PyTorch安装方案的容器化环境
# =============================================================================

version: '3.8'

services:
  # ========================================================================
  # 方案1：PyTorch升级版 (推荐⭐⭐⭐⭐⭐)
  # 特性：PyTorch 2.5.0 + CUDA 12.1 + 完整AI生态系统
  # ========================================================================
  pytorch-upgrade:
    build:
      context: .
      dockerfile: Dockerfile.pytorch-upgrade
      args:
        BUILDKIT_INLINE_CACHE: 1
    image: pytorch-solution:upgrade
    container_name: pytorch-upgrade-env
    hostname: pytorch-upgrade-host
    
    # GPU支持
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]
    
    # 端口映射
    ports:
      - "28888:8888"   # Jupyter Lab
      - "28080:8080"   # VS Code Web
      - "26006:6006"   # TensorBoard
      - "28000:8000"   # API服务
    
    # 卷挂载
    volumes:
      - ./workspace:/workspace
      - pytorch-upgrade-models:/workspace/models
      - pytorch-upgrade-datasets:/workspace/datasets
      - pytorch-upgrade-checkpoints:/workspace/checkpoints
      - ~/.gitconfig:/root/.gitconfig:ro
      - ~/.ssh:/root/.ssh:ro
    
    # 环境变量
    environment:
      - NVIDIA_VISIBLE_DEVICES=all
      - NVIDIA_DRIVER_CAPABILITIES=compute,utility
      - PYTHONPATH=/workspace
      - PYTHONUNBUFFERED=1
      - HF_HOME=/workspace/models/huggingface
      - HF_DATASETS_CACHE=/workspace/datasets
      - TRANSFORMERS_CACHE=/workspace/models/huggingface
      - CUDA_VISIBLE_DEVICES=0
      - PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:128
      - TZ=Asia/Shanghai
    
    networks:
      - pytorch-network
    
    shm_size: '16gb'
    mem_limit: 32g
    restart: unless-stopped
    
    healthcheck:
      test: ["CMD", "/bin/bash", "-c", "source /opt/miniconda/bin/activate llm_dev && python -c 'import torch; assert torch.cuda.is_available()'"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    
    working_dir: /workspace
    command: ["/bin/bash"]
    
    labels:
      - "solution=pytorch-upgrade"
      - "pytorch-version=2.5.0"
      - "cuda-version=12.1"
      - "recommendation=5"

  # ========================================================================
  # 方案2：渐进式安装版 (推荐⭐⭐⭐⭐⭐)
  # 特性：智能版本探测 + 自动降级 + 详细日志
  # ========================================================================
  pytorch-progressive:
    build:
      context: .
      dockerfile: Dockerfile.progressive-install
      args:
        BUILDKIT_INLINE_CACHE: 1
    image: pytorch-solution:progressive
    container_name: pytorch-progressive-env
    hostname: pytorch-progressive-host
    
    # GPU支持
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]
    
    # 端口映射
    ports:
      - "38888:8888"   # Jupyter Lab
      - "38080:8080"   # VS Code Web
      - "36006:6006"   # TensorBoard
      - "38000:8000"   # API服务
    
    # 卷挂载
    volumes:
      - ./workspace:/workspace
      - pytorch-progressive-models:/workspace/models
      - pytorch-progressive-datasets:/workspace/datasets
      - pytorch-progressive-checkpoints:/workspace/checkpoints
      - pytorch-progressive-logs:/var/log/pytorch-install
      - ~/.gitconfig:/root/.gitconfig:ro
      - ~/.ssh:/root/.ssh:ro
    
    # 环境变量
    environment:
      - NVIDIA_VISIBLE_DEVICES=all
      - NVIDIA_DRIVER_CAPABILITIES=compute,utility
      - PYTHONPATH=/workspace
      - PYTHONUNBUFFERED=1
      - HF_HOME=/workspace/models/huggingface
      - HF_DATASETS_CACHE=/workspace/datasets
      - TRANSFORMERS_CACHE=/workspace/models/huggingface
      - CUDA_VISIBLE_DEVICES=0
      - PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:128
      - DEBUG_MODE=true
      - TZ=Asia/Shanghai
    
    networks:
      - pytorch-network
    
    shm_size: '16gb'
    mem_limit: 32g
    restart: unless-stopped
    
    healthcheck:
      test: ["CMD", "/bin/bash", "-c", "source /opt/miniconda/bin/activate llm_dev && python -c 'import torch; assert torch.cuda.is_available()'"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 90s
    
    working_dir: /workspace
    command: ["/bin/bash"]
    
    labels:
      - "solution=pytorch-progressive"
      - "pytorch-version=auto"
      - "cuda-version=12.1"
      - "recommendation=5"

  # ========================================================================
  # 方案3：CUDA降级版 (兼容性⭐⭐⭐)
  # 特性：CUDA 11.8 + PyTorch 2.1.2 + 经典稳定组合
  # ========================================================================
  pytorch-cuda118:
    build:
      context: .
      dockerfile: Dockerfile.cuda-downgrade
      args:
        BUILDKIT_INLINE_CACHE: 1
    image: pytorch-solution:cuda118
    container_name: pytorch-cuda118-env
    hostname: pytorch-cuda118-host
    
    # GPU支持
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]
    
    # 端口映射  
    ports:
      - "48888:8888"   # Jupyter Lab
      - "48080:8080"   # VS Code Web
      - "46006:6006"   # TensorBoard
      - "48000:8000"   # API服务
    
    # 卷挂载
    volumes:
      - ./workspace:/workspace
      - pytorch-cuda118-models:/workspace/models
      - pytorch-cuda118-datasets:/workspace/datasets
      - pytorch-cuda118-checkpoints:/workspace/checkpoints
      - ~/.gitconfig:/root/.gitconfig:ro
      - ~/.ssh:/root/.ssh:ro
    
    # 环境变量
    environment:
      - NVIDIA_VISIBLE_DEVICES=all
      - NVIDIA_DRIVER_CAPABILITIES=compute,utility
      - PYTHONPATH=/workspace
      - PYTHONUNBUFFERED=1
      - HF_HOME=/workspace/models/huggingface
      - HF_DATASETS_CACHE=/workspace/datasets
      - TRANSFORMERS_CACHE=/workspace/models/huggingface
      - CUDA_VISIBLE_DEVICES=0
      - PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:128
      - TZ=Asia/Shanghai
    
    networks:
      - pytorch-network
    
    shm_size: '16gb'
    mem_limit: 32g
    restart: unless-stopped
    
    healthcheck:
      test: ["CMD", "/bin/bash", "-c", "source /opt/miniconda/bin/activate llm_dev && python -c 'import torch; assert torch.cuda.is_available(); assert torch.version.cuda == \"11.8\"'"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    
    working_dir: /workspace
    command: ["/bin/bash"]
    
    labels:
      - "solution=pytorch-cuda118"
      - "pytorch-version=2.1.2"
      - "cuda-version=11.8"
      - "recommendation=3"

  # ========================================================================
  # 辅助服务：Redis缓存
  # ========================================================================
  redis:
    image: redis:7-alpine
    container_name: pytorch-redis
    ports:
      - "6379:6379"
    volumes:
      - pytorch-redis-data:/data
    networks:
      - pytorch-network
    restart: unless-stopped
    command: redis-server --appendonly yes
    profiles:
      - cache

  # ========================================================================
  # 辅助服务：MinIO对象存储
  # ========================================================================
  minio:
    image: minio/minio:latest
    container_name: pytorch-minio
    environment:
      - MINIO_ROOT_USER=pytorch_admin
      - MINIO_ROOT_PASSWORD=pytorch_password123
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - pytorch-minio-data:/data
    networks:
      - pytorch-network
    restart: unless-stopped
    command: server /data --console-address ":9001"
    profiles:
      - storage

  # ========================================================================
  # 监控服务：Prometheus + Grafana
  # ========================================================================
  prometheus:
    image: prom/prometheus:latest
    container_name: pytorch-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - pytorch-prometheus-data:/prometheus
    networks:
      - pytorch-network
    restart: unless-stopped
    profiles:
      - monitoring

  grafana:
    image: grafana/grafana:latest
    container_name: pytorch-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=pytorch_admin
    volumes:
      - pytorch-grafana-data:/var/lib/grafana
    networks:
      - pytorch-network
    restart: unless-stopped
    profiles:
      - monitoring

# ========================================================================
# 网络配置
# ========================================================================
networks:
  pytorch-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# ========================================================================
# 数据卷配置
# ========================================================================
volumes:
  # PyTorch升级版数据卷
  pytorch-upgrade-models:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/pytorch-upgrade/models
  
  pytorch-upgrade-datasets:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/pytorch-upgrade/datasets
  
  pytorch-upgrade-checkpoints:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/pytorch-upgrade/checkpoints

  # 渐进式安装版数据卷
  pytorch-progressive-models:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/pytorch-progressive/models
  
  pytorch-progressive-datasets:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/pytorch-progressive/datasets
  
  pytorch-progressive-checkpoints:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/pytorch-progressive/checkpoints
  
  pytorch-progressive-logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/pytorch-progressive/logs

  # CUDA降级版数据卷
  pytorch-cuda118-models:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/pytorch-cuda118/models
  
  pytorch-cuda118-datasets:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/pytorch-cuda118/datasets
  
  pytorch-cuda118-checkpoints:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/pytorch-cuda118/checkpoints

  # 服务数据卷
  pytorch-redis-data:
    driver: local
  
  pytorch-minio-data:
    driver: local
  
  pytorch-prometheus-data:
    driver: local
  
  pytorch-grafana-data:
    driver: local