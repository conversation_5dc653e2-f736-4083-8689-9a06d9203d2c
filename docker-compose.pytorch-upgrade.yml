version: '3.8'

services:
  # PyTorch升级版AI开发环境
  pytorch-upgrade:
    build:
      context: .
      dockerfile: Dockerfile.robust
      args:
        # PyTorch升级版配置
        PYTORCH_VERSION: "2.5.0"
        PYTORCH_FALLBACK_VERSION: "2.4.1"
        TORCHVISION_VERSION: "0.20.0"
        TORCHAUDIO_VERSION: "2.5.0"
        # CUDA配置
        CUDA_VERSION: "12.1.1"
        CUDA_ARCH_LIST: "6.0;6.1;7.0;7.5;8.0;8.6;8.9;9.0"
        # Python和系统配置
        PYTHON_VERSION: "3.11"
        CONDA_ENV_NAME: "llm_dev"
        # 网络优化
        USE_TSINGHUA_MIRROR: "true"
        ENABLE_FALLBACK: "true"
    image: pytorch-upgrade:latest
    container_name: pytorch_upgrade_env
    hostname: pytorch-upgrade
    
    # GPU支持
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu, compute, utility]
    
    # 环境变量
    environment:
      # PyTorch升级版标识
      PYTORCH_UPGRADE_MODE: "true"
      PYTORCH_TARGET_VERSION: "2.5.0"
      
      # CUDA环境
      NVIDIA_VISIBLE_DEVICES: all
      NVIDIA_DRIVER_CAPABILITIES: compute,utility
      CUDA_VISIBLE_DEVICES: all
      
      # Python环境
      PYTHONPATH: /workspace:/opt/miniconda/envs/llm_dev/lib/python3.11/site-packages
      CONDA_DEFAULT_ENV: llm_dev
      
      # 性能优化
      OMP_NUM_THREADS: 8
      OPENBLAS_NUM_THREADS: 8
      MKL_NUM_THREADS: 8
      TORCH_CUDA_ARCH_LIST: "6.0;6.1;7.0;7.5;8.0;8.6;8.9;9.0"
      
      # 内存优化
      PYTORCH_CUDA_ALLOC_CONF: "max_split_size_mb:512"
      CUDA_LAUNCH_BLOCKING: 0
      
      # 网络优化
      PIP_INDEX_URL: "https://pypi.tuna.tsinghua.edu.cn/simple"
      CONDA_CHANNELS: "https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/,https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/free/,conda-forge"
      
      # 开发环境
      TERM: xterm-256color
      SHELL: /bin/bash
    
    # 卷挂载
    volumes:
      # 项目代码
      - ./:/workspace
      # Conda环境持久化
      - pytorch_conda_env:/opt/miniconda/envs
      - pytorch_conda_pkgs:/opt/miniconda/pkgs
      # 模型缓存
      - pytorch_model_cache:/root/.cache
      - pytorch_huggingface_cache:/root/.cache/huggingface
      # 开发工具配置
      - pytorch_vscode_config:/root/.vscode-server
      - pytorch_jupyter_config:/root/.jupyter
      # 系统缓存
      - pytorch_pip_cache:/root/.cache/pip
      - pytorch_npm_cache:/root/.npm
    
    # 网络配置
    networks:
      - pytorch_network
    
    # 端口映射
    ports:
      # Jupyter Lab
      - "8888:8888"
      # TensorBoard
      - "6006:6006"
      # FastAPI/Flask开发服务器
      - "8000:8000"
      - "5000:5000"
      # VSCode Server
      - "8080:8080"
      # Streamlit
      - "8501:8501"
      # MLflow
      - "5050:5050"
    
    # 启动命令
    command: /root/start.sh bash
    
    # 工作目录
    working_dir: /workspace
    
    # TTY支持
    tty: true
    stdin_open: true
    
    # 重启策略
    restart: unless-stopped
    
    # 健康检查
    healthcheck:
      test: ["CMD", "python", "-c", "import torch; print(f'PyTorch {torch.__version__} OK'); assert torch.cuda.is_available(), 'CUDA not available'"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s
    
    # 资源限制
    mem_limit: 16g
    memswap_limit: 20g
    shm_size: 2g
    
    # 标签
    labels:
      - "project=pytorch-upgrade"
      - "environment=development"
      - "pytorch.version=2.5.0"
      - "cuda.version=12.1.1"
      - "python.version=3.11"

  # 开发工具服务
  pytorch-tools:
    build:
      context: .
      dockerfile: Dockerfile.robust
    image: pytorch-upgrade:latest
    container_name: pytorch_tools
    
    depends_on:
      pytorch-upgrade:
        condition: service_healthy
    
    environment:
      PYTORCH_UPGRADE_MODE: "tools"
      NVIDIA_VISIBLE_DEVICES: all
    
    volumes:
      - ./:/workspace
      - pytorch_conda_env:/opt/miniconda/envs
      - pytorch_model_cache:/root/.cache
    
    networks:
      - pytorch_network
    
    ports:
      # 额外的开发端口
      - "9000:9000"
      - "9001:9001"
    
    command: >
      bash -c "
        echo '🛠️ PyTorch升级版开发工具服务启动...' &&
        source /opt/miniconda/bin/activate llm_dev &&
        echo '✅ 开发工具服务准备完成' &&
        tail -f /dev/null
      "
    
    restart: unless-stopped
    profiles: ["tools"]

# 网络配置
networks:
  pytorch_network:
    driver: bridge
    name: pytorch_upgrade_network
    ipam:
      config:
        - subnet: **********/16

# 卷配置
volumes:
  # Conda环境持久化
  pytorch_conda_env:
    name: pytorch_upgrade_conda_env
    driver: local
  
  pytorch_conda_pkgs:
    name: pytorch_upgrade_conda_pkgs
    driver: local
  
  # 缓存卷
  pytorch_model_cache:
    name: pytorch_upgrade_model_cache
    driver: local
  
  pytorch_huggingface_cache:
    name: pytorch_upgrade_hf_cache
    driver: local
  
  pytorch_pip_cache:
    name: pytorch_upgrade_pip_cache
    driver: local
  
  pytorch_npm_cache:
    name: pytorch_upgrade_npm_cache
    driver: local
  
  # 开发工具配置
  pytorch_vscode_config:
    name: pytorch_upgrade_vscode
    driver: local
  
  pytorch_jupyter_config:
    name: pytorch_upgrade_jupyter
    driver: local

# 扩展配置
x-common-variables: &common-env
  PYTORCH_UPGRADE_MODE: "true"
  PYTORCH_TARGET_VERSION: "2.5.0"
  NVIDIA_VISIBLE_DEVICES: all
  PYTHONPATH: /workspace:/opt/miniconda/envs/llm_dev/lib/python3.11/site-packages

x-common-volumes: &common-volumes
  - ./:/workspace
  - pytorch_conda_env:/opt/miniconda/envs
  - pytorch_model_cache:/root/.cache

# 开发配置文件
x-dev-config: &dev-config
  tty: true
  stdin_open: true
  restart: unless-stopped
  networks:
    - pytorch_network