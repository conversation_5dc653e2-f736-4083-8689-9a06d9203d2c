services:
  # LLM开发环境 (原 llm)
  llm-dev:
    build:
      context: .
      dockerfile: Dockerfile.robust
      args:
        BUILDKIT_INLINE_CACHE: 1
    image: llm-dev
    container_name: llm-dev-env
    hostname: llm-dev
    restart: unless-stopped
    
    # GPU支持
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]
    
    # 端口映射
    ports:
      - "8888:8888"   # Jupyter Lab
      - "5000:5000"   # MLflow UI
      - "8080:8080"   # Airflow Web UI
      - "3000:3000"   # Grafana
      - "9090:9090"   # Prometheus
      - "16686:16686" # Jaeger UI
    
    # 卷挂载 (Windows兼容路径)
    volumes:
      - type: bind
        source: ./workspace
        target: /workspace/project
      - type: bind
        source: ./data
        target: /workspace/data
      - type: bind
        source: ./models
        target: /workspace/models
      - type: bind
        source: ./mlruns
        target: /workspace/mlruns
      - type: bind
        source: ./logs
        target: /workspace/logs
      - llm-home:/root
      - llm-conda:/opt/miniconda
    
    # 环境变量
    environment:
      - NVIDIA_VISIBLE_DEVICES=all
      - CUDA_VISIBLE_DEVICES=0
      - PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:1024
      - MLFLOW_TRACKING_URI=file:///workspace/mlruns
      - AIRFLOW_HOME=/opt/airflow
      - POSTGRES_HOST=postgres
      - POSTGRES_PORT=5432
      - POSTGRES_DB=phoenix_v4
      - POSTGRES_USER=phoenix
      - POSTGRES_PASSWORD=phoenix_v4_2024
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - CLICKHOUSE_HOST=clickhouse
      - CLICKHOUSE_PORT=9000
      - NEO4J_HOST=neo4j
      - NEO4J_PORT=7687
    
    # 网络
    networks:
      - llm-network
    
    # 依赖服务
    depends_on:
      - postgres
      - redis
      - clickhouse
      - neo4j
      - prometheus
      - grafana
    
    # 健康检查
    healthcheck:
      test: ["CMD", "/bin/bash", "-c", "source /opt/miniconda/bin/activate ai && python -c 'import torch; assert torch.cuda.is_available()'"]
      interval: 60s
      timeout: 30s
      retries: 3
      start_period: 120s

  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    container_name: llm-postgres
    restart: unless-stopped
    
    environment:
      - POSTGRES_DB=phoenix_v4
      - POSTGRES_USER=phoenix
      - POSTGRES_PASSWORD=phoenix_v4_2024
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./sql/init:/docker-entrypoint-initdb.d
    
    ports:
      - "5432:5432"
    
    networks:
      - llm-network
    
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U phoenix -d phoenix_v4"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis缓存数据库
  redis:
    image: redis:7.2-alpine
    container_name: llm-redis
    restart: unless-stopped
    
    command: redis-server --appendonly yes --maxmemory 2gb --maxmemory-policy allkeys-lru
    
    volumes:
      - redis-data:/data
    
    ports:
      - "6379:6379"
    
    networks:
      - llm-network
    
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ClickHouse分析数据库
  clickhouse:
    image: clickhouse/clickhouse-server:latest
    container_name: llm-clickhouse
    restart: unless-stopped
    
    environment:
      - CLICKHOUSE_DB=phoenix_v4
      - CLICKHOUSE_USER=phoenix
      - CLICKHOUSE_PASSWORD=phoenix_v4_2024
    
    volumes:
      - clickhouse-data:/var/lib/clickhouse
      - ./clickhouse/config:/etc/clickhouse-server/config.d
    
    ports:
      - "9000:9000"   # Native protocol
      - "8123:8123"   # HTTP interface
    
    networks:
      - llm-network
    
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8123/ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Neo4j图数据库
  neo4j:
    image: neo4j:5.15-community
    container_name: llm-neo4j
    restart: unless-stopped
    
    environment:
      - NEO4J_AUTH=phoenix/phoenix_v4_2024
      - NEO4J_PLUGINS=["apoc", "graph-data-science"]
      - NEO4J_dbms_security_procedures_unrestricted=apoc.*,gds.*
    
    volumes:
      - neo4j-data:/data
      - neo4j-logs:/logs
    
    ports:
      - "7474:7474"   # HTTP
      - "7687:7687"   # Bolt
    
    networks:
      - llm-network
    
    healthcheck:
      test: ["CMD", "cypher-shell", "-u", "phoenix", "-p", "phoenix_v4_2024", "RETURN 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Prometheus监控
  prometheus:
    image: prom/prometheus:latest
    container_name: llm-prometheus
    restart: unless-stopped
    
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus-data:/prometheus
    
    ports:
      - "9090:9090"
    
    networks:
      - llm-network

  # Grafana可视化
  grafana:
    image: grafana/grafana:latest
    container_name: llm-grafana
    restart: unless-stopped
    
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=phoenix_v4_2024
      - GF_USERS_ALLOW_SIGN_UP=false
    
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    
    ports:
      - "3000:3000"
    
    networks:
      - llm-network
    
    depends_on:
      - prometheus

  # Jaeger分布式追踪
  jaeger:
    image: jaegertracing/all-in-one:latest
    container_name: llm-jaeger
    restart: unless-stopped
    
    environment:
      - COLLECTOR_OTLP_ENABLED=true
    
    ports:
      - "16686:16686"  # Jaeger UI
      - "14268:14268"  # Jaeger collector HTTP
      - "14250:14250"  # Jaeger collector gRPC
    
    networks:
      - llm-network

  # Apache Kafka消息队列
  kafka:
    image: confluentinc/cp-kafka:latest
    container_name: llm-kafka
    restart: unless-stopped
    
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
    
    ports:
      - "9092:9092"
    
    networks:
      - llm-network
    
    depends_on:
      - zookeeper

  # Zookeeper (Kafka依赖)
  zookeeper:
    image: confluentinc/cp-zookeeper:latest
    container_name: llm-zookeeper
    restart: unless-stopped
    
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    
    ports:
      - "2181:2181"
    
    networks:
      - llm-network

# 网络配置
networks:
  llm-network:
    driver: bridge
    name: llm-network

# 卷配置
volumes:
  # 应用数据
  llm-home:
    name: llm-home
  llm-conda:
    name: llm-conda
  
  # 数据库数据
  postgres-data:
    name: llm-postgres-data
  redis-data:
    name: llm-redis-data
  clickhouse-data:
    name: llm-clickhouse-data
  neo4j-data:
    name: llm-neo4j-data
  neo4j-logs:
    name: llm-neo4j-logs
  
  # 监控数据
  prometheus-data:
    name: llm-prometheus-data
  grafana-data:
    name: llm-grafana-data
