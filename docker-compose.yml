# Docker Compose配置文件
# 凤凰涅槃计划V3：终极AI开发环境 - 专为中国大陆优化
# 支持CUDA GPU计算、Go微服务、C++高性能、Python AI/ML
#
# 使用方法:
#   docker-compose up -d              # 后台启动
#   docker-compose exec multi-lang zsh  # 进入开发环境
#   docker-compose down               # 停止服务

version: '3.8'

services:
  # 凤凰涅槃计划V3多语言AI开发环境
  multi-lang:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        # 构建参数
        BUILDKIT_INLINE_CACHE: 1
    image: multi-lang:latest
    container_name: multi-lang-environment
    hostname: multi-lang-host
    
    # GPU支持 (需要nvidia-docker)
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]
    
    # 端口映射
    ports:
      - "18888:8888"   # Jupyter Lab
      - "18080:8080"   # VS Code Web
      - "16006:6006"   # TensorBoard
      - "18000:8000"   # vLLM API服务
      - "13000:3000"   # 开发服务器 (可选)
      - "15000:5000"   # Flask/FastAPI (可选)
    
    # 卷挂载
    volumes:
      # 工作目录持久化
      - ./workspace:/workspace
      # 模型缓存持久化
      - ai-models:/workspace/models
      # 数据集持久化  
      - ai-datasets:/workspace/datasets
      # 检查点持久化
      - ai-checkpoints:/workspace/checkpoints
      # 项目代码持久化
      - ai-projects:/workspace/projects
      # Git配置
      - ~/.gitconfig:/root/.gitconfig:ro
      # SSH密钥 (可选)
      - ~/.ssh:/root/.ssh:ro
      # Docker socket (用于容器内Docker操作)
      - /var/run/docker.sock:/var/run/docker.sock
    
    # 环境变量
    environment:
      # NVIDIA相关
      - NVIDIA_VISIBLE_DEVICES=all
      - NVIDIA_DRIVER_CAPABILITIES=compute,utility

      # Python环境
      - PYTHONPATH=/workspace
      - PYTHONUNBUFFERED=1

      # Go环境
      - GOPATH=/workspace/go
      - GOROOT=/usr/local/go
      - GOPROXY=https://goproxy.cn,direct
      - GOSUMDB=sum.golang.google.cn

      # Rust环境
      - RUSTUP_HOME=/root/.rustup
      - CARGO_HOME=/root/.cargo
      - RUSTUP_DIST_SERVER=https://mirrors.tuna.tsinghua.edu.cn/rustup

      # HuggingFace配置
      - HF_HOME=/workspace/models/huggingface
      - HF_DATASETS_CACHE=/workspace/datasets
      - TRANSFORMERS_CACHE=/workspace/models/huggingface
      - HF_HUB_CACHE=/workspace/models/huggingface

      # CUDA优化
      - CUDA_VISIBLE_DEVICES=0
      - PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:128
      - TOKENIZERS_PARALLELISM=false
      
      # 开发环境
      - WORKSPACE=/workspace
      - SHELL=/bin/zsh
      
      # 时区设置
      - TZ=Asia/Shanghai
    
    # 网络配置
    networks:
      - ai-network
    
    # 共享内存大小 (大模型训练需要)
    shm_size: '16gb'
    
    # 内存限制 (根据实际情况调整)
    mem_limit: 32g
    
    # 重启策略
    restart: unless-stopped
    
    # 健康检查
    healthcheck:
      test: ["CMD", "nvidia-smi", "--query-gpu=name", "--format=csv,noheader"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    
    # 安全配置
    security_opt:
      - seccomp:unconfined
    
    # 特权模式 (GPU访问需要)
    privileged: false
    
    # 工作目录
    working_dir: /workspace
    
    # 默认命令
    command: ["/bin/zsh"]
    
    # 标签
    labels:
      - "project=ai-development"
      - "environment=multi-language"
      - "gpu=enabled"

  # Redis缓存服务 (可选，用于分布式训练)
  redis:
    image: redis:7-alpine
    container_name: ai-redis
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - ai-network
    restart: unless-stopped
    command: redis-server --appendonly yes
    profiles:
      - cache

  # PostgreSQL数据库 (可选，用于实验管理)
  postgres:
    image: postgres:15-alpine
    container_name: ai-postgres
    environment:
      - POSTGRES_DB=ai_experiments
      - POSTGRES_USER=ai_user
      - POSTGRES_PASSWORD=ai_password
    ports:
      - "5432:5432"
    volumes:
      - postgres-data:/var/lib/postgresql/data
    networks:
      - ai-network
    restart: unless-stopped
    profiles:
      - database

  # MinIO对象存储 (可选，用于大文件存储)
  minio:
    image: minio/minio:latest
    container_name: ai-minio
    environment:
      - MINIO_ROOT_USER=ai_admin
      - MINIO_ROOT_PASSWORD=ai_password123
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio-data:/data
    networks:
      - ai-network
    restart: unless-stopped
    command: server /data --console-address ":9001"
    profiles:
      - storage

# 网络配置
networks:
  ai-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 数据卷配置
volumes:
  # AI相关数据卷
  ai-models:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/models
  
  ai-datasets:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/datasets
  
  ai-checkpoints:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/checkpoints
  
  ai-projects:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/projects
  
  # 服务数据卷
  redis-data:
    driver: local
  
  postgres-data:
    driver: local
  
  minio-data:
    driver: local
