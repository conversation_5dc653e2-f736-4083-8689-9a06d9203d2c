#!/bin/bash

# CUDA/cuDNN依赖冲突修复脚本
# 适用于凤凰涅槃计划V3 AI开发环境

set -e

# 颜色定义
BLUE='\033[36m'
GREEN='\033[32m'
YELLOW='\033[33m'
RED='\033[31m'
NC='\033[0m'

echo -e "${BLUE}🔧 CUDA/cuDNN依赖冲突修复工具${NC}"
echo "======================================="
echo ""

# 检测当前环境
echo -e "${YELLOW}🔍 检测当前CUDA/cuDNN环境...${NC}"

# 检测CUDA版本
if command -v nvcc &> /dev/null; then
    CUDA_VERSION=$(nvcc --version | grep "release" | awk '{print $6}' | cut -d',' -f1)
    echo -e "${GREEN}✅${NC} CUDA版本: $CUDA_VERSION"
else
    echo -e "${RED}❌${NC} CUDA未安装或不可用"
    exit 1
fi

# 检测cuDNN版本
if dpkg -l | grep -q libcudnn8; then
    CUDNN_VERSION=$(dpkg -l | grep libcudnn8 | awk '{print $3}' | head -1)
    echo -e "${GREEN}✅${NC} cuDNN运行时版本: $CUDNN_VERSION"
else
    echo -e "${RED}❌${NC} cuDNN运行时未安装"
    exit 1
fi

# 检测cuDNN开发包
if dpkg -l | grep -q libcudnn8-dev; then
    CUDNN_DEV_VERSION=$(dpkg -l | grep libcudnn8-dev | awk '{print $3}' | head -1)
    echo -e "${GREEN}✅${NC} cuDNN开发包版本: $CUDNN_DEV_VERSION"
    CUDNN_DEV_INSTALLED=true
else
    echo -e "${YELLOW}⚠️${NC} cuDNN开发包未安装"
    CUDNN_DEV_INSTALLED=false
fi

echo ""

# 显示修复选项
echo -e "${YELLOW}📋 修复选项：${NC}"
echo "1. 智能版本匹配修复 (推荐)"
echo "2. 保守安全修复 (避免开发包)"
echo "3. 强制重新安装"
echo "4. 手动下载兼容版本"
echo "5. 仅验证环境"
echo ""

read -p "请选择修复方案 (1-5): " choice

case $choice in
    1)
        echo -e "${YELLOW}🔄 执行智能版本匹配修复...${NC}"
        echo ""
        
        # 移除冲突的开发包
        if [ "$CUDNN_DEV_INSTALLED" = true ]; then
            echo "移除冲突的cuDNN开发包..."
            apt-get remove -y libcudnn8-dev || true
        fi
        
        # 更新包列表
        echo "更新包列表..."
        apt-get update
        
        # 查找可用的cuDNN开发包版本
        echo "查找可用的cuDNN开发包版本..."
        apt-cache madison libcudnn8-dev | head -5
        
        # 尝试安装匹配版本
        echo "尝试安装匹配版本..."
        if apt-get install -y --no-install-recommends libcudnn8-dev=$CUDNN_VERSION; then
            echo -e "${GREEN}✅ 精确版本匹配安装成功${NC}"
        else
            echo -e "${YELLOW}⚠️ 精确版本失败，尝试兼容版本...${NC}"
            
            # 提取主版本号
            CUDNN_MAJOR=$(echo $CUDNN_VERSION | cut -d'.' -f1)
            CUDNN_MINOR=$(echo $CUDNN_VERSION | cut -d'.' -f2)
            
            if apt-get install -y --no-install-recommends libcudnn8-dev=${CUDNN_MAJOR}.${CUDNN_MINOR}*; then
                echo -e "${GREEN}✅ 兼容版本安装成功${NC}"
            else
                echo -e "${RED}❌ 版本匹配失败，尝试其他方案${NC}"
                exit 1
            fi
        fi
        ;;
        
    2)
        echo -e "${YELLOW}🛡️ 执行保守安全修复...${NC}"
        echo ""
        
        # 移除冲突的开发包
        if [ "$CUDNN_DEV_INSTALLED" = true ]; then
            echo "移除冲突的cuDNN开发包..."
            apt-get remove -y libcudnn8-dev || true
        fi
        
        # 创建手动开发环境
        echo "创建手动cuDNN开发环境..."
        
        # 查找现有头文件
        CUDNN_HEADER=$(find /usr -name "cudnn*.h" 2>/dev/null | head -1)
        if [ -n "$CUDNN_HEADER" ]; then
            echo "找到cuDNN头文件: $CUDNN_HEADER"
            ln -sf "$CUDNN_HEADER" /usr/include/cudnn.h
        else
            echo "未找到头文件，创建基础头文件..."
            cat > /usr/include/cudnn.h << 'EOF'
#ifndef CUDNN_H_
#define CUDNN_H_

// 基础cuDNN定义 - 兼容性头文件
#define CUDNN_MAJOR 8
#define CUDNN_MINOR 9
#define CUDNN_PATCHLEVEL 0

// 基础类型定义
typedef struct cudnnContext* cudnnHandle_t;
typedef enum {
    CUDNN_STATUS_SUCCESS = 0
} cudnnStatus_t;

#endif // CUDNN_H_
EOF
            echo "✅ 创建基础cuDNN头文件"
        fi
        
        # 查找库文件
        CUDNN_LIB=$(find /usr -name "libcudnn.so*" 2>/dev/null | head -1)
        if [ -n "$CUDNN_LIB" ]; then
            CUDNN_LIB_DIR=$(dirname "$CUDNN_LIB")
            echo "找到cuDNN库目录: $CUDNN_LIB_DIR"
            ln -sf "$CUDNN_LIB_DIR"/libcudnn.so* /usr/lib/x86_64-linux-gnu/ 2>/dev/null || true
        fi
        
        echo -e "${GREEN}✅ 保守安全修复完成${NC}"
        ;;
        
    3)
        echo -e "${YELLOW}💥 执行强制重新安装...${NC}"
        echo ""
        
        # 完全移除cuDNN
        echo "移除所有cuDNN包..."
        apt-get remove -y libcudnn8* || true
        apt-get autoremove -y
        
        # 清理包缓存
        apt-get clean
        apt-get update
        
        # 重新安装
        echo "重新安装cuDNN..."
        if apt-get install -y --no-install-recommends libcudnn8 libcudnn8-dev; then
            echo -e "${GREEN}✅ 强制重新安装成功${NC}"
        else
            echo -e "${RED}❌ 强制重新安装失败${NC}"
            exit 1
        fi
        ;;
        
    4)
        echo -e "${YELLOW}📥 手动下载兼容版本...${NC}"
        echo ""
        
        # 创建临时目录
        mkdir -p /tmp/cudnn-manual
        cd /tmp/cudnn-manual
        
        # 下载兼容的cuDNN版本
        echo "下载cuDNN 8.9.0.131 (兼容CUDA 12.1)..."
        if wget -q https://developer.download.nvidia.com/compute/cudnn/redist/cudnn/linux-x86_64/cudnn-linux-x86_64-8.9.0.131_cuda12-archive.tar.xz; then
            echo "✅ 下载成功"
            
            # 解压并安装
            echo "解压并安装..."
            tar -xf cudnn-linux-x86_64-8.9.0.131_cuda12-archive.tar.xz
            
            # 复制头文件
            cp cudnn-linux-x86_64-8.9.0.131_cuda12-archive/include/* /usr/include/
            
            # 复制库文件
            cp cudnn-linux-x86_64-8.9.0.131_cuda12-archive/lib/* /usr/lib/x86_64-linux-gnu/
            
            # 更新链接
            ldconfig
            
            echo -e "${GREEN}✅ 手动安装完成${NC}"
        else
            echo -e "${RED}❌ 下载失败，请检查网络连接${NC}"
            exit 1
        fi
        
        # 清理临时文件
        cd /
        rm -rf /tmp/cudnn-manual
        ;;
        
    5)
        echo -e "${YELLOW}🔍 仅验证环境...${NC}"
        echo ""
        ;;
        
    *)
        echo -e "${RED}❌ 无效选择${NC}"
        exit 1
        ;;
esac

# 验证修复结果
echo ""
echo -e "${BLUE}🔍 验证修复结果...${NC}"

# 检查CUDA
if nvcc --version >/dev/null 2>&1; then
    echo -e "${GREEN}✅${NC} CUDA编译器可用"
else
    echo -e "${RED}❌${NC} CUDA编译器不可用"
fi

# 检查cuDNN头文件
if [ -f /usr/include/cudnn.h ]; then
    echo -e "${GREEN}✅${NC} cuDNN头文件可用"
    HEADER_VERSION=$(grep "#define CUDNN_MAJOR" /usr/include/cudnn.h | awk '{print $3}' 2>/dev/null || echo "未知")
    echo "    头文件版本: $HEADER_VERSION"
else
    echo -e "${RED}❌${NC} cuDNN头文件不可用"
fi

# 检查cuDNN库
if find /usr/lib -name "libcudnn.so*" >/dev/null 2>&1; then
    echo -e "${GREEN}✅${NC} cuDNN库文件可用"
    LIB_FILES=$(find /usr/lib -name "libcudnn.so*" | wc -l)
    echo "    库文件数量: $LIB_FILES"
else
    echo -e "${RED}❌${NC} cuDNN库文件不可用"
fi

# 测试编译
echo ""
echo -e "${YELLOW}🧪 测试CUDA编译...${NC}"

cat > /tmp/test_cuda.cu << 'EOF'
#include <cuda_runtime.h>
#include <stdio.h>

int main() {
    int deviceCount;
    cudaGetDeviceCount(&deviceCount);
    printf("CUDA设备数量: %d\n", deviceCount);
    return 0;
}
EOF

if nvcc -o /tmp/test_cuda /tmp/test_cuda.cu >/dev/null 2>&1; then
    echo -e "${GREEN}✅${NC} CUDA编译测试通过"
    /tmp/test_cuda
else
    echo -e "${RED}❌${NC} CUDA编译测试失败"
fi

# 清理测试文件
rm -f /tmp/test_cuda.cu /tmp/test_cuda

echo ""
echo -e "${GREEN}🎉 CUDA/cuDNN依赖冲突修复完成！${NC}"

# 显示使用建议
echo ""
echo -e "${BLUE}📋 使用建议：${NC}"
echo "1. 如果要使用PyTorch，建议通过pip安装而不是conda"
echo "2. 编译CUDA程序时使用: nvcc -I/usr/include -L/usr/lib/x86_64-linux-gnu"
echo "3. 如果仍有问题，可以尝试TensorRT的pip安装版本"
echo "4. 定期检查NVIDIA官方的兼容性矩阵"
