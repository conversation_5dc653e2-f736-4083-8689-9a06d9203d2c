#!/bin/bash
# 修复 nvidia-smi 构建时错误的脚本
# 凤凰涅槃计划V4 - Docker构建优化

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_header() {
    echo -e "${BLUE}🔧 $1${NC}"
    echo "=================================================="
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️ $1${NC}"
}

# 备份原始文件
backup_dockerfile() {
    print_header "备份Dockerfile"
    
    if [ ! -f "Dockerfile.robust.nvidia-smi-fix.backup" ]; then
        cp Dockerfile.robust Dockerfile.robust.nvidia-smi-fix.backup
        print_success "已备份 Dockerfile.robust"
    else
        print_warning "备份文件已存在"
    fi
}

# 修复构建时的nvidia-smi检查
fix_build_time_nvidia_smi() {
    print_header "修复构建时nvidia-smi检查"
    
    print_info "将构建时的nvidia-smi检查替换为仅检查CUDA编译器"
    
    # 替换构建时的CUDA验证
    sed -i '/# 验证CUDA环境 (RTX 4070s兼容性检查)/,/echo "✅ CUDA环境验证完成"/c\
# 验证CUDA环境 (RTX 4070s兼容性检查)\
RUN echo "🔍 验证CUDA环境..." && \\\
    echo "检查CUDA编译器..." && \\\
    nvcc --version && \\\
    echo "检查CUDA库路径..." && \\\
    ls -la /usr/local/cuda*/lib64/libcudart.so* && \\\
    echo "检查cuDNN库..." && \\\
    (ls -la /usr/lib/x86_64-linux-gnu/libcudnn.so* || echo "cuDNN将在后续阶段安装") && \\\
    echo "✅ CUDA构建环境验证完成 (GPU运行时检查将在容器启动时进行)"' Dockerfile.robust
    
    print_success "已修复构建时CUDA验证"
}

# 创建运行时GPU检查脚本
create_runtime_gpu_check() {
    print_header "创建运行时GPU检查脚本"
    
    cat > gpu_runtime_check.sh << 'EOF'
#!/bin/bash
# GPU运行时兼容性检查脚本
# 在容器启动时验证GPU和CUDA环境

echo "🔍 GPU运行时兼容性检查..."
echo "=================================="

# 检查NVIDIA驱动
if command -v nvidia-smi >/dev/null 2>&1; then
    echo "✅ NVIDIA驱动可用"
    echo "GPU信息:"
    nvidia-smi --query-gpu=name,memory.total,driver_version --format=csv,noheader
    echo ""
else
    echo "❌ nvidia-smi不可用 - 请检查:"
    echo "   1. NVIDIA驱动是否已安装"
    echo "   2. Docker是否使用 --gpus all 参数"
    echo "   3. nvidia-container-toolkit是否已安装"
    echo ""
fi

# 检查CUDA编译器
if command -v nvcc >/dev/null 2>&1; then
    echo "✅ CUDA编译器可用"
    nvcc --version | grep "release"
    echo ""
else
    echo "❌ CUDA编译器不可用"
    echo ""
fi

# 检查Python CUDA支持
if command -v python >/dev/null 2>&1; then
    echo "🐍 Python CUDA支持检查..."
    python << 'PYTHON_EOF'
try:
    import torch
    print(f"✅ PyTorch版本: {torch.__version__}")
    
    if torch.cuda.is_available():
        print(f"✅ CUDA可用: {torch.version.cuda}")
        print(f"✅ GPU数量: {torch.cuda.device_count()}")
        if torch.cuda.device_count() > 0:
            print(f"✅ GPU名称: {torch.cuda.get_device_name(0)}")
            
            # 简单的GPU计算测试
            x = torch.randn(1000, 1000).cuda()
            y = torch.randn(1000, 1000).cuda()
            z = torch.mm(x, y)
            print("✅ GPU计算测试通过")
    else:
        print("❌ PyTorch无法访问CUDA")
        print("   请检查GPU驱动和Docker GPU支持")
        
except ImportError:
    print("⚠️ PyTorch未安装或AI环境未激活")
    print("   请运行: source /opt/miniconda/bin/activate ai")
except Exception as e:
    print(f"❌ GPU测试失败: {e}")
PYTHON_EOF
else
    echo "⚠️ Python不可用，跳过CUDA Python检查"
fi

echo ""
echo "🎯 GPU兼容性检查完成"
echo "=================================="
EOF

    chmod +x gpu_runtime_check.sh
    print_success "已创建运行时GPU检查脚本: gpu_runtime_check.sh"
}

# 更新Dockerfile以包含运行时检查脚本
update_dockerfile_with_runtime_check() {
    print_header "更新Dockerfile以包含运行时检查"
    
    # 在COPY指令附近添加GPU检查脚本
    sed -i '/COPY install_postgresql.sh \/tmp\/install_postgresql.sh/a\COPY gpu_runtime_check.sh /usr/local/bin/gpu_runtime_check.sh' Dockerfile.robust
    
    # 在启动脚本中添加GPU检查选项
    sed -i '/echo "🔧 GPU状态: nvidia-smi"/c\    echo "🔧 GPU状态: nvidia-smi 或 gpu_runtime_check.sh"' Dockerfile.robust
    
    print_success "已更新Dockerfile包含运行时检查脚本"
}

# 创建智能CUDA验证函数
create_smart_cuda_verification() {
    print_header "创建智能CUDA验证函数"
    
    cat > smart_cuda_check.py << 'EOF'
#!/usr/bin/env python3
"""
智能CUDA环境验证脚本
适用于构建时和运行时的CUDA环境检查
"""

import subprocess
import sys
import os

def run_command(cmd, description=""):
    """安全执行命令"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=30)
        return result.returncode == 0, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return False, "", f"命令超时: {cmd}"
    except Exception as e:
        return False, "", f"执行错误: {e}"

def check_cuda_compiler():
    """检查CUDA编译器"""
    print("🔍 检查CUDA编译器...")
    success, stdout, stderr = run_command("nvcc --version")
    
    if success:
        version_line = [line for line in stdout.split('\n') if 'release' in line.lower()]
        if version_line:
            print(f"✅ CUDA编译器: {version_line[0].strip()}")
            return True
    
    print(f"❌ CUDA编译器检查失败: {stderr}")
    return False

def check_cuda_libraries():
    """检查CUDA库文件"""
    print("🔍 检查CUDA库文件...")
    
    # 检查关键CUDA库
    cuda_libs = [
        "/usr/local/cuda*/lib64/libcudart.so*",
        "/usr/local/cuda*/lib64/libcublas.so*",
        "/usr/local/cuda*/lib64/libcurand.so*"
    ]
    
    found_libs = 0
    for lib_pattern in cuda_libs:
        success, stdout, _ = run_command(f"ls {lib_pattern} 2>/dev/null")
        if success and stdout.strip():
            found_libs += 1
            lib_name = lib_pattern.split('/')[-1].replace('*', '').replace('.so', '')
            print(f"✅ 找到库: {lib_name}")
    
    if found_libs >= 2:
        print(f"✅ CUDA库检查通过 ({found_libs}/{len(cuda_libs)})")
        return True
    else:
        print(f"⚠️ CUDA库不完整 ({found_libs}/{len(cuda_libs)})")
        return False

def check_nvidia_smi():
    """检查nvidia-smi (仅运行时)"""
    print("🔍 检查NVIDIA驱动...")
    success, stdout, stderr = run_command("nvidia-smi --query-gpu=name --format=csv,noheader")
    
    if success and stdout.strip():
        gpu_name = stdout.strip().split('\n')[0]
        print(f"✅ GPU检测到: {gpu_name}")
        return True
    else:
        print("⚠️ nvidia-smi不可用 (这在Docker构建时是正常的)")
        return False

def check_python_cuda():
    """检查Python CUDA支持"""
    print("🔍 检查Python CUDA支持...")
    
    python_check = '''
try:
    import torch
    print(f"PyTorch版本: {torch.__version__}")
    
    if torch.cuda.is_available():
        print(f"CUDA版本: {torch.version.cuda}")
        print(f"GPU数量: {torch.cuda.device_count()}")
        print("CUDA_AVAILABLE")
    else:
        print("CUDA_NOT_AVAILABLE")
except ImportError:
    print("PYTORCH_NOT_FOUND")
except Exception as e:
    print(f"ERROR: {e}")
'''
    
    success, stdout, stderr = run_command(f"python -c '{python_check}'")
    
    if success:
        if "CUDA_AVAILABLE" in stdout:
            print("✅ Python CUDA支持正常")
            return True
        elif "PYTORCH_NOT_FOUND" in stdout:
            print("⚠️ PyTorch未安装 (可能需要激活AI环境)")
            return False
        else:
            print("⚠️ Python CUDA支持有问题")
            return False
    else:
        print("⚠️ Python检查失败")
        return False

def main():
    print("🎯 智能CUDA环境验证")
    print("=" * 50)
    
    # 检测运行环境
    is_build_time = os.environ.get('DOCKER_BUILDKIT') or os.environ.get('BUILDKIT_FRONTEND')
    is_runtime = os.path.exists('/proc/driver/nvidia') or os.path.exists('/dev/nvidia0')
    
    if is_build_time:
        print("🏗️ 检测到构建时环境")
    elif is_runtime:
        print("🚀 检测到运行时环境")
    else:
        print("🔍 环境类型未知，执行通用检查")
    
    print()
    
    # 执行检查
    checks = []
    
    # 基础CUDA检查 (构建时和运行时都执行)
    checks.append(("CUDA编译器", check_cuda_compiler()))
    checks.append(("CUDA库文件", check_cuda_libraries()))
    
    # 运行时检查
    if not is_build_time:
        checks.append(("NVIDIA驱动", check_nvidia_smi()))
        checks.append(("Python CUDA", check_python_cuda()))
    
    # 总结结果
    print()
    print("📊 检查结果总结:")
    print("-" * 30)
    
    passed = sum(1 for _, result in checks if result)
    total = len(checks)
    
    for name, result in checks:
        status = "✅" if result else "❌"
        print(f"{status} {name}")
    
    print(f"\n通过率: {passed}/{total} ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 所有检查通过！")
        return 0
    elif passed >= total * 0.7:
        print("⚠️ 大部分检查通过，可能需要运行时验证")
        return 0
    else:
        print("❌ 多项检查失败，需要排查问题")
        return 1

if __name__ == "__main__":
    sys.exit(main())
EOF

    chmod +x smart_cuda_check.py
    print_success "已创建智能CUDA验证脚本: smart_cuda_check.py"
}

# 更新Dockerfile使用智能验证
update_dockerfile_smart_verification() {
    print_header "更新Dockerfile使用智能验证"
    
    # 添加智能验证脚本到Dockerfile
    sed -i '/COPY gpu_runtime_check.sh \/usr\/local\/bin\/gpu_runtime_check.sh/a\COPY smart_cuda_check.py /usr/local/bin/smart_cuda_check.py' Dockerfile.robust
    
    # 在Python环境验证中添加智能CUDA检查
    sed -i '/echo "✅ Python环境验证完成"/i\    # 智能CUDA环境验证\n    python /usr/local/bin/smart_cuda_check.py && \\' Dockerfile.robust
    
    print_success "已更新Dockerfile使用智能验证"
}

# 创建使用指南
create_usage_guide() {
    print_header "创建使用指南"
    
    cat > NVIDIA_SMI_FIX_GUIDE.md << 'EOF'
# 🔧 nvidia-smi 构建错误修复指南

## 🎯 问题说明

在Docker构建过程中，`nvidia-smi` 命令不可用是正常现象，因为：

1. **构建时限制**: Docker构建阶段无法访问GPU硬件
2. **驱动依赖**: `nvidia-smi` 需要NVIDIA驱动，只在运行时可用
3. **安全隔离**: 构建过程与宿主机硬件隔离

## ✅ 修复方案

### 1. 构建时验证 (已修复)
```dockerfile
# 只验证CUDA编译器和库文件
RUN nvcc --version && \
    ls -la /usr/local/cuda*/lib64/libcudart.so*
```

### 2. 运行时验证
```bash
# 容器启动后验证GPU
docker run --rm --gpus all phoenix-v4-expert:latest nvidia-smi

# 或使用我们的智能检查脚本
docker run --rm --gpus all phoenix-v4-expert:latest smart_cuda_check.py
```

## 🚀 使用方法

### 构建镜像
```bash
# 现在可以正常构建，不会因nvidia-smi失败
docker-compose -f docker-compose.v4.yml build --no-cache
```

### 运行时GPU检查
```bash
# 方法1: 使用nvidia-smi
docker run --rm --gpus all phoenix-v4-expert:latest nvidia-smi

# 方法2: 使用智能检查脚本
docker run --rm --gpus all phoenix-v4-expert:latest gpu_runtime_check.sh

# 方法3: 使用Python CUDA检查
docker run --rm --gpus all phoenix-v4-expert:latest \
    bash -c "source /opt/miniconda/bin/activate ai && python -c 'import torch; print(torch.cuda.is_available())'"
```

### 完整环境验证
```bash
# 启动容器并进入交互模式
docker run -it --gpus all phoenix-v4-expert:latest bash

# 在容器内运行完整检查
smart_cuda_check.py
```

## 📊 验证层级

1. **构建时**: CUDA编译器 + 库文件
2. **运行时**: GPU驱动 + 硬件访问
3. **应用层**: PyTorch/JAX CUDA支持

## 🔧 故障排除

### 如果运行时仍然无法访问GPU:

1. **检查Docker GPU支持**:
   ```bash
   docker run --rm --gpus all nvidia/cuda:12.2.2-base nvidia-smi
   ```

2. **检查NVIDIA Container Toolkit**:
   ```bash
   sudo systemctl status nvidia-container-toolkit
   ```

3. **检查驱动版本**:
   ```bash
   nvidia-smi
   # 需要 >= 525.60.13 支持CUDA 12.2
   ```

## 🎉 修复效果

- ✅ 构建过程不再因nvidia-smi失败
- ✅ 运行时可以正常访问GPU
- ✅ 智能检查脚本提供详细诊断
- ✅ 分层验证确保环境完整性
EOF

    print_success "已创建使用指南: NVIDIA_SMI_FIX_GUIDE.md"
}

# 验证修复效果
verify_fix() {
    print_header "验证修复效果"
    
    # 检查Dockerfile修改
    if ! grep -q "nvidia-smi" Dockerfile.robust; then
        print_success "构建时nvidia-smi检查已移除"
    else
        print_warning "Dockerfile中仍包含nvidia-smi，请检查"
    fi
    
    # 检查脚本文件
    local scripts=("gpu_runtime_check.sh" "smart_cuda_check.py")
    for script in "${scripts[@]}"; do
        if [ -f "$script" ]; then
            print_success "已创建: $script"
        else
            print_error "缺失: $script"
        fi
    done
    
    # 检查Dockerfile语法
    if command -v docker >/dev/null 2>&1; then
        if docker build -f Dockerfile.robust -t test-nvidia-fix . --target base-system >/dev/null 2>&1; then
            print_success "Dockerfile语法检查通过"
            docker rmi test-nvidia-fix >/dev/null 2>&1 || true
        else
            print_error "Dockerfile语法检查失败"
        fi
    else
        print_warning "Docker不可用，跳过语法检查"
    fi
}

# 主函数
main() {
    print_header "修复nvidia-smi构建错误"
    
    echo "本脚本将修复以下问题："
    echo "1. 移除构建时的nvidia-smi检查"
    echo "2. 创建运行时GPU验证脚本"
    echo "3. 添加智能CUDA环境检查"
    echo "4. 更新Dockerfile使用新的验证方式"
    echo ""
    
    read -p "是否继续执行修复？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_info "修复已取消"
        exit 0
    fi
    
    # 执行修复步骤
    backup_dockerfile
    fix_build_time_nvidia_smi
    create_runtime_gpu_check
    update_dockerfile_with_runtime_check
    create_smart_cuda_verification
    update_dockerfile_smart_verification
    create_usage_guide
    verify_fix
    
    print_header "修复完成"
    print_success "nvidia-smi构建错误已修复！"
    print_info "现在可以正常构建Docker镜像："
    echo "  docker-compose -f docker-compose.v4.yml build --no-cache"
    print_info "运行时GPU验证："
    echo "  docker run --rm --gpus all phoenix-v4-expert:latest smart_cuda_check.py"
}

# 执行主函数
main "$@"
