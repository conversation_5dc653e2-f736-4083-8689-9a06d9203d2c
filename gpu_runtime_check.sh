#!/bin/bash
# GPU运行时兼容性检查脚本
# 在容器启动时验证GPU和CUDA环境

echo "🔍 GPU运行时兼容性检查..."
echo "=================================="

# 检查NVIDIA驱动
if command -v nvidia-smi >/dev/null 2>&1; then
    echo "✅ NVIDIA驱动可用"
    echo "GPU信息:"
    nvidia-smi --query-gpu=name,memory.total,driver_version --format=csv,noheader
    echo ""
else
    echo "❌ nvidia-smi不可用 - 请检查:"
    echo "   1. NVIDIA驱动是否已安装"
    echo "   2. Docker是否使用 --gpus all 参数"
    echo "   3. nvidia-container-toolkit是否已安装"
    echo ""
fi

# 检查CUDA编译器
if command -v nvcc >/dev/null 2>&1; then
    echo "✅ CUDA编译器可用"
    nvcc --version | grep "release"
    echo ""
else
    echo "❌ CUDA编译器不可用"
    echo ""
fi

# 检查Python CUDA支持
if command -v python >/dev/null 2>&1; then
    echo "🐍 Python CUDA支持检查..."
    python << 'PYTHON_EOF'
try:
    import torch
    print(f"✅ PyTorch版本: {torch.__version__}")
    
    if torch.cuda.is_available():
        print(f"✅ CUDA可用: {torch.version.cuda}")
        print(f"✅ GPU数量: {torch.cuda.device_count()}")
        if torch.cuda.device_count() > 0:
            print(f"✅ GPU名称: {torch.cuda.get_device_name(0)}")
            
            # 简单的GPU计算测试
            x = torch.randn(1000, 1000).cuda()
            y = torch.randn(1000, 1000).cuda()
            z = torch.mm(x, y)
            print("✅ GPU计算测试通过")
    else:
        print("❌ PyTorch无法访问CUDA")
        print("   请检查GPU驱动和Docker GPU支持")
        
except ImportError:
    print("⚠️ PyTorch未安装或AI环境未激活")
    print("   请运行: source /opt/miniconda/bin/activate ai")
except Exception as e:
    print(f"❌ GPU测试失败: {e}")
PYTHON_EOF
else
    echo "⚠️ Python不可用，跳过CUDA Python检查"
fi

echo ""
echo "🎯 GPU兼容性检查完成"
echo "=================================="
