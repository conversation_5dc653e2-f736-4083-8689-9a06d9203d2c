#!/bin/bash
# 智能Miniconda安装脚本 - 凤凰涅槃计划V4
# 实现"官方源优先，阿里云备选"策略

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置变量
MINICONDA_VERSION="latest"
MINICONDA_FILENAME="Miniconda3-${MINICONDA_VERSION}-Linux-x86_64.sh"
INSTALL_DIR="/opt/miniconda"
TEMP_DIR="/tmp"
MAX_RETRIES=3
TIMEOUT=300  # 5分钟超时

# 下载源配置 (官方源优先策略)
DOWNLOAD_SOURCES=(
    # 官方源 (优先)
    "https://repo.anaconda.com/miniconda/${MINICONDA_FILENAME}"
    "https://repo.continuum.io/miniconda/${MINICONDA_FILENAME}"
    
    # 国内镜像源 (备选)
    "https://mirrors.tuna.tsinghua.edu.cn/anaconda/miniconda/${MINICONDA_FILENAME}"
    "https://mirrors.aliyun.com/anaconda/miniconda/${MINICONDA_FILENAME}"
    "https://mirrors.ustc.edu.cn/anaconda/miniconda/${MINICONDA_FILENAME}"
    "https://mirror.bjtu.edu.cn/anaconda/miniconda/${MINICONDA_FILENAME}"
    
    # 备用官方源
    "https://conda.anaconda.org/anaconda/miniconda/${MINICONDA_FILENAME}"
)

# 检查URL可用性
check_url_availability() {
    local url="$1"
    local timeout="${2:-10}"
    
    log_info "检查URL可用性: $url"
    
    # 使用HEAD请求检查URL
    if timeout "$timeout" curl -sSL --head "$url" >/dev/null 2>&1; then
        log_success "URL可用: $url"
        return 0
    else
        log_warning "URL不可用: $url"
        return 1
    fi
}

# 下载文件
download_file() {
    local url="$1"
    local output_file="$2"
    local max_retries="${3:-$MAX_RETRIES}"
    
    log_info "开始下载: $url"
    
    for ((i=1; i<=max_retries; i++)); do
        log_info "尝试下载 ($i/$max_retries)..."
        
        # 使用wget下载，支持断点续传
        if timeout "$TIMEOUT" wget \
            --continue \
            --progress=bar:force \
            --timeout=60 \
            --tries=3 \
            --user-agent="Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36" \
            "$url" -O "$output_file" 2>/dev/null; then
            
            log_success "下载成功: $output_file"
            return 0
        else
            log_warning "下载失败 (尝试 $i/$max_retries)"
            [ -f "$output_file" ] && rm -f "$output_file"
            sleep $((i * 2))  # 递增延迟
        fi
    done
    
    log_error "下载失败，已尝试 $max_retries 次"
    return 1
}

# 验证下载文件
verify_download() {
    local file="$1"
    
    log_info "验证下载文件: $file"
    
    # 检查文件是否存在
    if [ ! -f "$file" ]; then
        log_error "文件不存在: $file"
        return 1
    fi
    
    # 检查文件大小 (Miniconda应该至少50MB)
    local file_size=$(stat -c%s "$file" 2>/dev/null || echo "0")
    local min_size=$((50 * 1024 * 1024))  # 50MB
    
    if [ "$file_size" -lt "$min_size" ]; then
        log_error "文件大小异常: $file_size bytes (期望 > $min_size bytes)"
        return 1
    fi
    
    # 检查文件头 (应该是shell脚本)
    if ! head -1 "$file" | grep -q "^#!/bin/bash\|^#!/bin/sh"; then
        log_error "文件格式异常: 不是有效的shell脚本"
        return 1
    fi
    
    log_success "文件验证通过: $file ($file_size bytes)"
    return 0
}

# 智能下载Miniconda
smart_download_miniconda() {
    local output_file="$TEMP_DIR/miniconda.sh"
    
    log_info "🐍 开始智能下载Miniconda..."
    log_info "目标文件: $MINICONDA_FILENAME"
    log_info "安装目录: $INSTALL_DIR"
    
    # 清理旧文件
    [ -f "$output_file" ] && rm -f "$output_file"
    
    # 尝试每个下载源
    for source_url in "${DOWNLOAD_SOURCES[@]}"; do
        log_info "🌐 尝试下载源: $source_url"
        
        # 检查URL可用性 (快速检查)
        if check_url_availability "$source_url" 10; then
            # 尝试下载
            if download_file "$source_url" "$output_file" 2; then
                # 验证下载
                if verify_download "$output_file"; then
                    log_success "✅ Miniconda下载成功！"
                    echo "$output_file"
                    return 0
                else
                    log_warning "下载文件验证失败，尝试下一个源"
                    [ -f "$output_file" ] && rm -f "$output_file"
                fi
            else
                log_warning "下载失败，尝试下一个源"
            fi
        else
            log_warning "URL不可用，跳过此源"
        fi
        
        # 短暂延迟后尝试下一个源
        sleep 2
    done
    
    log_error "❌ 所有下载源都失败了！"
    return 1
}

# 安装Miniconda
install_miniconda() {
    local installer_file="$1"
    
    log_info "🔧 开始安装Miniconda..."
    
    # 检查安装目录
    if [ -d "$INSTALL_DIR" ]; then
        log_warning "安装目录已存在，将先备份"
        mv "$INSTALL_DIR" "${INSTALL_DIR}.backup.$(date +%s)" || true
    fi
    
    # 执行安装
    log_info "执行安装脚本..."
    if bash "$installer_file" -b -p "$INSTALL_DIR"; then
        log_success "✅ Miniconda安装成功！"
        
        # 清理安装文件
        rm -f "$installer_file"
        
        # 验证安装
        if [ -f "$INSTALL_DIR/bin/conda" ]; then
            log_success "conda可执行文件验证通过"
            
            # 显示版本信息
            local conda_version=$("$INSTALL_DIR/bin/conda" --version 2>/dev/null || echo "未知版本")
            log_info "安装版本: $conda_version"
            
            return 0
        else
            log_error "conda可执行文件未找到"
            return 1
        fi
    else
        log_error "❌ Miniconda安装失败！"
        return 1
    fi
}

# 配置conda镜像源
configure_conda_channels() {
    local conda_bin="$INSTALL_DIR/bin/conda"
    
    log_info "🌐 配置conda镜像源..."
    
    # V4策略：官方源优先，国内镜像备选
    local channels=(
        # 官方源 (优先)
        "conda-forge"
        "defaults"
        
        # 国内镜像 (备选)
        "https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/"
        "https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/free/"
        "https://mirrors.aliyun.com/anaconda/pkgs/main/"
        "https://mirrors.aliyun.com/anaconda/pkgs/free/"
    )
    
    # 清除现有配置
    "$conda_bin" config --remove-key channels 2>/dev/null || true
    
    # 添加镜像源 (逆序添加，优先级高的最后添加)
    for ((i=${#channels[@]}-1; i>=0; i--)); do
        local channel="${channels[i]}"
        log_info "添加镜像源: $channel"
        "$conda_bin" config --add channels "$channel" || log_warning "添加镜像源失败: $channel"
    done
    
    # 设置其他配置
    "$conda_bin" config --set show_channel_urls yes
    "$conda_bin" config --set channel_priority strict
    
    log_success "✅ conda镜像源配置完成"
    
    # 显示配置
    log_info "当前镜像源配置:"
    "$conda_bin" config --show channels || true
}

# 创建AI环境
create_ai_environment() {
    local conda_bin="$INSTALL_DIR/bin/conda"
    
    log_info "🧠 创建AI开发环境..."
    
    # 创建Python 3.10环境
    if "$conda_bin" create -n ai python=3.10 -y; then
        log_success "✅ AI环境创建成功"
        
        # 验证环境
        if [ -d "$INSTALL_DIR/envs/ai" ]; then
            log_success "AI环境目录验证通过"
            
            # 显示Python版本
            local python_version=$("$INSTALL_DIR/envs/ai/bin/python" --version 2>/dev/null || echo "未知版本")
            log_info "Python版本: $python_version"
            
            return 0
        else
            log_error "AI环境目录未找到"
            return 1
        fi
    else
        log_error "❌ AI环境创建失败"
        return 1
    fi
}

# 主函数
main() {
    log_info "🚀 开始智能Miniconda安装 - 凤凰涅槃计划V4"
    log_info "策略: 官方源优先，国内镜像备选"
    echo "=================================================="
    
    # 检查系统环境
    log_info "检查系统环境..."
    if ! command -v wget >/dev/null 2>&1; then
        log_error "wget未安装，请先安装wget"
        exit 1
    fi
    
    if ! command -v curl >/dev/null 2>&1; then
        log_error "curl未安装，请先安装curl"
        exit 1
    fi
    
    # 创建临时目录
    mkdir -p "$TEMP_DIR"
    cd "$TEMP_DIR"
    
    # 执行安装步骤
    local installer_file
    if installer_file=$(smart_download_miniconda); then
        if install_miniconda "$installer_file"; then
            configure_conda_channels
            create_ai_environment
            
            log_success "🎉 Miniconda智能安装完成！"
            log_info "安装路径: $INSTALL_DIR"
            log_info "激活命令: source $INSTALL_DIR/bin/activate ai"
            
            return 0
        else
            log_error "安装失败"
            return 1
        fi
    else
        log_error "下载失败"
        return 1
    fi
}

# 执行主函数
main "$@"
