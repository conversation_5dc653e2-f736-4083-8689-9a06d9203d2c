#!/bin/bash
# PostgreSQL安装脚本 - 支持多种安装方式
# 适用于Ubuntu 22.04和其他Debian系发行版

set -e

POSTGRESQL_VERSION=${1:-15}
echo "🐘 开始安装PostgreSQL ${POSTGRESQL_VERSION}..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 重试函数
retry_cmd() {
    local max_attempts=3
    local attempt=1
    local cmd="$@"
    
    while [ $attempt -le $max_attempts ]; do
        print_info "尝试执行: $cmd (第 $attempt 次)"
        if $cmd; then
            return 0
        else
            print_warning "命令失败，等待5秒后重试..."
            sleep 5
            ((attempt++))
        fi
    done
    
    print_error "命令执行失败，已重试 $max_attempts 次"
    return 1
}

# 检查包是否可用
check_package_available() {
    local package_name="$1"
    if apt-cache show "$package_name" >/dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# 方法1：尝试从默认Ubuntu源安装
install_from_default_repo() {
    print_info "尝试从默认Ubuntu源安装PostgreSQL..."
    
    retry_cmd apt-get update
    
    # 检查是否有PostgreSQL包可用
    if check_package_available "postgresql-${POSTGRESQL_VERSION}"; then
        print_info "找到PostgreSQL ${POSTGRESQL_VERSION}，开始安装..."
        retry_cmd apt-get install -y --no-install-recommends \
            postgresql-${POSTGRESQL_VERSION} \
            postgresql-client-${POSTGRESQL_VERSION} \
            postgresql-contrib-${POSTGRESQL_VERSION}
        return 0
    else
        print_warning "默认源中没有PostgreSQL ${POSTGRESQL_VERSION}"
        return 1
    fi
}

# 方法2：从PostgreSQL官方源安装
install_from_official_repo() {
    print_info "从PostgreSQL官方源安装..."
    
    # 安装必要的工具
    retry_cmd apt-get install -y --no-install-recommends \
        wget ca-certificates gnupg lsb-release
    
    # 添加PostgreSQL官方GPG密钥
    print_info "添加PostgreSQL官方GPG密钥..."
    if ! wget --quiet -O - https://www.postgresql.org/media/keys/ACCC4CF8.asc | \
         gpg --dearmor | tee /etc/apt/trusted.gpg.d/postgresql.gpg >/dev/null; then
        print_error "无法下载PostgreSQL GPG密钥"
        return 1
    fi
    
    # 添加PostgreSQL官方源
    local codename=$(lsb_release -cs)
    print_info "添加PostgreSQL官方源 (${codename})..."
    echo "deb http://apt.postgresql.org/pub/repos/apt/ ${codename}-pgdg main" > /etc/apt/sources.list.d/pgdg.list
    
    # 更新包列表
    retry_cmd apt-get update
    
    # 安装PostgreSQL
    print_info "从官方源安装PostgreSQL ${POSTGRESQL_VERSION}..."
    retry_cmd apt-get install -y --no-install-recommends \
        postgresql-${POSTGRESQL_VERSION} \
        postgresql-client-${POSTGRESQL_VERSION} \
        postgresql-contrib-${POSTGRESQL_VERSION}
    
    return 0
}

# 方法3：安装通用版本（不指定版本号）
install_generic_version() {
    print_info "安装通用版本的PostgreSQL..."
    
    retry_cmd apt-get update
    retry_cmd apt-get install -y --no-install-recommends \
        postgresql \
        postgresql-client \
        postgresql-contrib
    
    return 0
}

# 安装PostGIS扩展（可选）
install_postgis() {
    print_info "尝试安装PostGIS扩展..."
    
    local postgis_packages=(
        "postgresql-${POSTGRESQL_VERSION}-postgis-3"
        "postgresql-${POSTGRESQL_VERSION}-postgis"
        "postgis"
    )
    
    for package in "${postgis_packages[@]}"; do
        if check_package_available "$package"; then
            print_info "安装PostGIS包: $package"
            if apt-get install -y --no-install-recommends "$package"; then
                print_info "PostGIS安装成功"
                return 0
            fi
        fi
    done
    
    print_warning "PostGIS安装失败或不可用，跳过"
    return 1
}

# 配置PostgreSQL
configure_postgresql() {
    print_info "配置PostgreSQL..."
    
    # 创建数据目录
    mkdir -p /var/lib/postgresql/data
    chown -R postgres:postgres /var/lib/postgresql
    
    # 创建日志目录
    mkdir -p /var/log/postgresql
    chown -R postgres:postgres /var/log/postgresql
    
    print_info "PostgreSQL配置完成"
}

# 验证安装
verify_installation() {
    print_info "验证PostgreSQL安装..."
    
    # 检查PostgreSQL版本
    if command -v psql >/dev/null 2>&1; then
        local version=$(psql --version | head -n1)
        print_info "PostgreSQL客户端版本: $version"
    else
        print_error "PostgreSQL客户端未正确安装"
        return 1
    fi
    
    # 检查服务器二进制文件
    if command -v postgres >/dev/null 2>&1; then
        print_info "PostgreSQL服务器已安装"
    else
        print_warning "PostgreSQL服务器可能未正确安装"
    fi
    
    print_info "PostgreSQL安装验证完成"
    return 0
}

# 主安装流程
main() {
    print_info "开始PostgreSQL ${POSTGRESQL_VERSION}安装流程..."
    
    # 尝试不同的安装方法
    if install_from_default_repo; then
        print_info "从默认源安装成功"
    elif install_from_official_repo; then
        print_info "从官方源安装成功"
    elif install_generic_version; then
        print_info "通用版本安装成功"
    else
        print_error "所有安装方法都失败了"
        exit 1
    fi
    
    # 尝试安装PostGIS
    install_postgis
    
    # 配置PostgreSQL
    configure_postgresql
    
    # 验证安装
    if verify_installation; then
        print_info "🎉 PostgreSQL安装完成！"
    else
        print_error "PostgreSQL安装验证失败"
        exit 1
    fi
}

# 执行主函数
main "$@"
