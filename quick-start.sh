#!/bin/bash
# =============================================================================
# PyTorch版本冲突解决方案 - 快速启动脚本
# 一键部署和测试PyTorch环境的完整解决方案
# =============================================================================

set -euo pipefail

# 颜色输出配置
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $*"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $*"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $*"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $*"
}

log_header() {
    echo -e "${PURPLE}========================================${NC}"
    echo -e "${WHITE}$*${NC}"
    echo -e "${PURPLE}========================================${NC}"
}

# 全局变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
DATA_DIR="$SCRIPT_DIR/data"
LOG_DIR="$SCRIPT_DIR/logs"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# 解决方案配置
declare -A SOLUTIONS=(
    ["upgrade"]="PyTorch升级版 (推荐⭐⭐⭐⭐⭐)"
    ["progressive"]="渐进式安装版 (智能⭐⭐⭐⭐⭐)"
    ["cuda118"]="CUDA降级版 (兼容⭐⭐⭐)"
)

declare -A SOLUTION_PORTS=(
    ["upgrade"]="28888:8888"
    ["progressive"]="38888:8888"
    ["cuda118"]="48888:8888"
)

declare -A SOLUTION_DESCRIPTIONS=(
    ["upgrade"]="PyTorch 2.5.0 + CUDA 12.1 + 完整AI生态系统，性能最优"
    ["progressive"]="智能版本探测 + 自动降级，兼容性最佳"
    ["cuda118"]="CUDA 11.8 + PyTorch 2.1.2，经典稳定组合"
)

# 初始化函数
initialize() {
    log_header "PyTorch版本冲突解决方案 - 快速启动"
    
    # 检查Docker
    if ! command -v docker >/dev/null 2>&1; then
        log_error "Docker未安装！请先安装Docker。"
        exit 1
    fi
    
    # 检查Docker Compose
    if ! command -v docker-compose >/dev/null 2>&1 && ! docker compose version >/dev/null 2>&1; then
        log_error "Docker Compose未安装！请先安装Docker Compose。" 
        exit 1
    fi
    
    # 创建必要目录
    mkdir -p "$DATA_DIR" "$LOG_DIR"
    for solution in "${!SOLUTIONS[@]}"; do
        mkdir -p "$DATA_DIR/pytorch-$solution"/{models,datasets,checkpoints}
    done
    
    log_success "环境检查完成"
}

# 显示解决方案菜单
show_solutions() {
    log_header "可用解决方案"
    echo ""
    
    local i=1
    for solution in upgrade progressive cuda118; do
        echo -e "  ${CYAN}$i)${NC} ${SOLUTIONS[$solution]}"
        echo -e "     ${solution_descriptions[$solution]:-${SOLUTION_DESCRIPTIONS[$solution]}}"
        echo -e "     访问地址: http://localhost:${SOLUTION_PORTS[$solution]%:*}"
        echo ""
        ((i++))
    done
    
    echo -e "  ${CYAN}4)${NC} 全部部署并测试"
    echo -e "  ${CYAN}5)${NC} 仅运行测试脚本"
    echo -e "  ${CYAN}6)${NC} 清理环境"
    echo -e "  ${CYAN}0)${NC} 退出"
}

# 部署单个解决方案
deploy_solution() {
    local solution=$1
    local solution_name="pytorch-$solution"
    
    log_header "部署解决方案: ${SOLUTIONS[$solution]}"
    
    # 检查Dockerfile是否存在
    local dockerfile="Dockerfile.pytorch-$solution"
    if [[ "$solution" == "cuda118" ]]; then
        dockerfile="Dockerfile.cuda-downgrade"
    elif [[ "$solution" == "progressive" ]]; then
        dockerfile="Dockerfile.progressive-install"
    fi
    
    if [[ ! -f "$dockerfile" ]]; then
        log_error "Dockerfile不存在: $dockerfile"
        return 1
    fi
    
    # 构建镜像
    log_info "开始构建镜像: $solution_name"
    local build_log="$LOG_DIR/build_${solution}_${TIMESTAMP}.log"
    
    if docker build -f "$dockerfile" -t "pytorch-solution:$solution" . 2>&1 | tee "$build_log"; then
        log_success "镜像构建成功: pytorch-solution:$solution"
    else
        log_error "镜像构建失败，检查日志: $build_log"
        return 1
    fi
    
    # 启动容器
    log_info "启动容器: $solution_name"
    local port_mapping="${SOLUTION_PORTS[$solution]}"
    
    if docker run -d \
        --name "$solution_name" \
        --gpus all \
        -p "$port_mapping" \
        -v "$DATA_DIR/pytorch-$solution:/workspace/data" \
        pytorch-solution:$solution; then
        log_success "容器启动成功: $solution_name"
        log_info "访问地址: http://localhost:${port_mapping%:*}"
    else
        log_error "容器启动失败: $solution_name"
        return 1
    fi
    
    # 等待容器启动
    log_info "等待容器初始化..."
    sleep 10
    
    # 验证容器健康状态
    if docker exec "$solution_name" bash -c "source /opt/miniconda/bin/activate llm_dev && python -c 'import torch; print(f\"PyTorch {torch.__version__} 已准备就绪\")'"; then
        log_success "容器验证成功: $solution_name"
        return 0
    else
        log_warning "容器验证失败，但容器已启动: $solution_name"
        return 1
    fi
}

# 使用Docker Compose部署
deploy_with_compose() {
    local solution=$1
    
    log_header "使用Docker Compose部署: ${SOLUTIONS[$solution]}"
    
    if [[ ! -f "docker-compose.pytorch-fixed.yml" ]]; then
        log_error "Docker Compose配置文件不存在: docker-compose.pytorch-fixed.yml"
        return 1
    fi
    
    # 启动指定服务
    local service_name="pytorch-$solution"
    
    log_info "启动服务: $service_name"
    if docker-compose -f docker-compose.pytorch-fixed.yml up -d "$service_name"; then
        log_success "服务启动成功: $service_name"
        
        # 等待服务就绪
        log_info "等待服务初始化..."
        sleep 15
        
        # 检查服务状态
        if docker-compose -f docker-compose.pytorch-fixed.yml ps "$service_name" | grep -q "Up"; then
            log_success "服务运行正常: $service_name"
            local port="${SOLUTION_PORTS[$solution]%:*}"
            log_info "Jupyter访问地址: http://localhost:$port"
            return 0
        else
            log_error "服务启动异常: $service_name"
            return 1
        fi
    else
        log_error "服务启动失败: $service_name" 
        return 1
    fi
}

# 运行测试
run_tests() {
    local solution=$1
    
    log_header "测试解决方案: ${SOLUTIONS[$solution]}"
    
    if [[ -x "./test-pytorch-solutions.sh" ]]; then
        log_info "运行自动化测试..."
        if ./test-pytorch-solutions.sh "$solution"; then
            log_success "测试通过: $solution"
            return 0
        else
            log_error "测试失败: $solution"
            return 1
        fi
    else
        log_warning "测试脚本不存在或不可执行: ./test-pytorch-solutions.sh"
        return 1
    fi
}

# 全部部署
deploy_all() {
    log_header "部署所有解决方案"
    
    local success_count=0
    local total_count=${#SOLUTIONS[@]}
    
    for solution in "${!SOLUTIONS[@]}"; do
        log_info "部署方案 $((++i))/$total_count: $solution"
        if deploy_with_compose "$solution"; then
            ((success_count++))
        fi
        echo ""
    done
    
    log_header "部署总结"
    log_info "成功部署: $success_count/$total_count 个方案"
    
    if [[ $success_count -eq $total_count ]]; then
        log_success "所有方案部署成功！"
        show_access_info
    else
        log_warning "部分方案部署失败，请检查日志"
    fi
}

# 显示访问信息
show_access_info() {
    log_header "服务访问信息"
    echo ""
    
    for solution in "${!SOLUTIONS[@]}"; do
        local port="${SOLUTION_PORTS[$solution]%:*}"
        local status=""
        
        # 检查容器状态
        if docker ps --format "table {{.Names}}" | grep -q "pytorch-$solution"; then
            status="${GREEN}运行中${NC}"
        else
            status="${RED}未运行${NC}"
        fi
        
        echo -e "  ${CYAN}${SOLUTIONS[$solution]}${NC}"
        echo -e "    状态: $status"
        echo -e "    Jupyter: http://localhost:$port"
        echo -e "    容器: pytorch-$solution-env"
        echo ""
    done
    
    echo -e "  ${YELLOW}辅助服务:${NC}"
    echo -e "    Redis: redis://localhost:6379 (需要启用cache profile)"
    echo -e "    MinIO: http://localhost:9000 (需要启用storage profile)"
    echo -e "    Grafana: http://localhost:3000 (需要启用monitoring profile)"
}

# 清理环境
cleanup() {
    log_header "清理环境"
    
    # 停止并删除容器
    log_info "停止并删除容器..."
    for solution in "${!SOLUTIONS[@]}"; do
        local container_name="pytorch-$solution-env"
        if docker ps -a --format "table {{.Names}}" | grep -q "$container_name"; then
            log_info "删除容器: $container_name"
            docker stop "$container_name" >/dev/null 2>&1 || true
            docker rm "$container_name" >/dev/null 2>&1 || true
        fi
    done
    
    # 使用Docker Compose清理
    if [[ -f "docker-compose.pytorch-fixed.yml" ]]; then
        log_info "Docker Compose清理..."
        docker-compose -f docker-compose.pytorch-fixed.yml down -v --remove-orphans >/dev/null 2>&1 || true
    fi
    
    # 删除镜像
    log_info "删除构建的镜像..."
    for solution in "${!SOLUTIONS[@]}"; do
        local image_name="pytorch-solution:$solution"
        if docker images --format "table {{.Repository}}:{{.Tag}}" | grep -q "$image_name"; then
            docker rmi "$image_name" >/dev/null 2>&1 || true
        fi
    done
    
    # 清理未使用的Docker资源
    log_info "清理未使用的Docker资源..."
    docker system prune -f >/dev/null 2>&1 || true
    
    log_success "环境清理完成"
}

# 显示帮助信息
show_help() {
    echo "使用方法: $0 [OPTIONS] [SOLUTION]"
    echo ""
    echo "选项:"
    echo "  -h, --help          显示帮助信息"
    echo "  -c, --cleanup       清理环境"
    echo "  -t, --test          运行测试"
    echo "  -a, --all           部署所有方案"
    echo "  -i, --info          显示访问信息"
    echo ""
    echo "解决方案:"
    for solution in "${!SOLUTIONS[@]}"; do
        echo "  $solution           ${SOLUTIONS[$solution]}"
    done
    echo ""
    echo "示例:"
    echo "  $0 upgrade          # 部署PyTorch升级版"
    echo "  $0 --all            # 部署所有方案"
    echo "  $0 --test upgrade   # 测试特定方案"
    echo "  $0 --cleanup        # 清理环境"
}

# 主函数
main() {
    # 解析命令行参数
    case "${1:-}" in
        -h|--help)
            show_help
            exit 0
            ;;
        -c|--cleanup)
            initialize
            cleanup
            exit 0
            ;;
        -t|--test)
            initialize
            if [[ -n "${2:-}" ]]; then
                run_tests "$2"
            else
                log_error "请指定要测试的方案"
                show_help
                exit 1
            fi
            exit 0
            ;;
        -a|--all)
            initialize
            deploy_all
            exit 0
            ;;
        -i|--info)
            show_access_info
            exit 0
            ;;
        upgrade|progressive|cuda118)
            initialize
            if deploy_with_compose "$1"; then
                log_success "方案 $1 部署成功！"
                show_access_info
            else
                log_error "方案 $1 部署失败！"
                exit 1
            fi
            exit 0
            ;;
        "")
            # 交互模式
            initialize
            while true; do
                echo ""
                show_solutions
                echo ""
                read -p "请选择方案 (0-6): " choice
                
                case $choice in
                    1)
                        deploy_with_compose "upgrade"
                        ;;
                    2)
                        deploy_with_compose "progressive"
                        ;;
                    3)
                        deploy_with_compose "cuda118"
                        ;;
                    4)
                        deploy_all
                        ;;
                    5)
                        echo "请输入要测试的方案 (upgrade/progressive/cuda118):"
                        read test_solution
                        if [[ -n "$test_solution" ]]; then
                            run_tests "$test_solution"
                        fi
                        ;;
                    6)
                        cleanup
                        ;;
                    0)
                        log_info "退出"
                        exit 0
                        ;;
                    *)
                        log_warning "无效选择，请重新选择"
                        ;;
                esac
                
                echo ""
                read -p "按Enter键继续..."
            done
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
}

# 信号处理
trap cleanup EXIT

# 执行主函数
main "$@"