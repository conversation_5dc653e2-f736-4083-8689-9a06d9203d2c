#!/bin/bash

# 凤凰涅槃计划V3：终极AI开发环境设置脚本
# 支持CUDA GPU计算、Go微服务、C++高性能、Python AI/ML
#
# 使用方法:
#   chmod +x setup-environment.sh
#   ./setup-environment.sh

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否在中国大陆
check_china_environment() {
    log_info "检查网络环境..."
    
    if curl -s --connect-timeout 5 https://mirrors.tuna.tsinghua.edu.cn > /dev/null; then
        log_success "✅ 中国大陆网络环境检查通过"
    else
        log_error "❌ 无法访问中国大陆镜像源，请检查网络连接"
        exit 1
    fi
}

# 检查系统要求
check_requirements() {
    log_info "检查系统要求..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    # 检查NVIDIA Docker (可选)
    if command -v nvidia-smi &> /dev/null; then
        if docker run --rm --gpus all nvidia/cuda:11.0-base nvidia-smi &> /dev/null; then
            log_success "✅ NVIDIA Docker支持已启用"
        else
            log_warning "⚠️ NVIDIA Docker支持未启用，将无法使用GPU加速"
        fi
    else
        log_warning "⚠️ 未检测到NVIDIA GPU"
    fi
    
    log_success "✅ 系统要求检查完成"
}

# 创建必要的目录结构
create_directories() {
    log_info "创建目录结构..."
    
    # 创建数据目录
    mkdir -p data/{models,datasets,checkpoints,projects}
    mkdir -p workspace/{notebooks,scripts,configs,logs}
    
    # 设置权限
    chmod -R 755 data workspace
    
    log_success "✅ 目录结构创建完成"
}

# 创建环境配置文件
create_env_file() {
    log_info "创建环境配置文件..."
    
    cat > .env << EOF
# Python AI开发环境配置
# 请根据实际情况修改以下配置

# 项目配置
PROJECT_NAME=python-ai-dev
COMPOSE_PROJECT_NAME=ai-dev

# GPU配置
NVIDIA_VISIBLE_DEVICES=all
CUDA_VISIBLE_DEVICES=0

# 内存配置
SHARED_MEMORY_SIZE=16g
MEMORY_LIMIT=32g

# 端口配置
JUPYTER_PORT=8888
VSCODE_PORT=8080
TENSORBOARD_PORT=6006
VLLM_PORT=8000
DEV_PORT=3000
API_PORT=5000

# 数据库配置 (可选服务)
POSTGRES_DB=ai_experiments
POSTGRES_USER=ai_user
POSTGRES_PASSWORD=ai_password

# MinIO配置 (可选服务)
MINIO_ROOT_USER=ai_admin
MINIO_ROOT_PASSWORD=ai_password123

# HuggingFace配置
HF_TOKEN=your_huggingface_token_here

# 时区配置
TZ=Asia/Shanghai
EOF
    
    log_success "✅ 环境配置文件创建完成"
}

# 创建快捷脚本
create_shortcuts() {
    log_info "创建快捷脚本..."
    
    # 启动脚本
    cat > start.sh << 'EOF'
#!/bin/bash
echo "🚀 启动Python AI开发环境..."
docker-compose up -d ai-dev
echo "✅ 环境启动完成！"
echo ""
echo "📋 可用服务:"
echo "  🔗 Jupyter Lab:    http://localhost:8888"
echo "  🔗 VS Code Web:    http://localhost:8080"
echo "  🔗 TensorBoard:    http://localhost:6006"
echo "  🔗 vLLM API:       http://localhost:8000"
echo ""
echo "💻 进入开发环境:"
echo "  docker-compose exec ai-dev zsh"
EOF
    
    # 停止脚本
    cat > stop.sh << 'EOF'
#!/bin/bash
echo "🛑 停止Python AI开发环境..."
docker-compose down
echo "✅ 环境已停止"
EOF
    
    # 进入环境脚本
    cat > enter.sh << 'EOF'
#!/bin/bash
echo "🔧 进入AI开发环境..."
docker-compose exec ai-dev zsh
EOF
    
    # 查看日志脚本
    cat > logs.sh << 'EOF'
#!/bin/bash
echo "📋 查看环境日志..."
docker-compose logs -f ai-dev
EOF
    
    # 重建环境脚本
    cat > rebuild.sh << 'EOF'
#!/bin/bash
echo "🔄 重建AI开发环境..."
docker-compose down
docker-compose build --no-cache ai-dev
docker-compose up -d ai-dev
echo "✅ 环境重建完成！"
EOF
    
    # 设置执行权限
    chmod +x start.sh stop.sh enter.sh logs.sh rebuild.sh
    
    log_success "✅ 快捷脚本创建完成"
}

# 创建使用说明
create_readme() {
    log_info "创建使用说明..."
    
    cat > DOCKER_USAGE.md << 'EOF'
# 🚀 Python AI开发环境使用指南

## 📋 快速开始

### 1. 启动环境
```bash
./start.sh
# 或者
docker-compose up -d ai-dev
```

### 2. 进入开发环境
```bash
./enter.sh
# 或者
docker-compose exec ai-dev zsh
```

### 3. 停止环境
```bash
./stop.sh
# 或者
docker-compose down
```

## 🌐 可用服务

| 服务 | 端口 | 访问地址 | 说明 |
|------|------|----------|------|
| Jupyter Lab | 8888 | http://localhost:8888 | 交互式开发环境 |
| VS Code Web | 8080 | http://localhost:8080 | Web版VS Code |
| TensorBoard | 6006 | http://localhost:6006 | 模型可视化 |
| vLLM API | 8000 | http://localhost:8000 | 大模型推理API |

## 💻 Python开发

### Python AI/ML开发
```bash
cd /workspace/templates/python
python -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

### 运行示例
```bash
python /workspace/examples/ai-ml/pytorch_example.py
```

### CUDA GPU编程
```bash
cd /workspace/templates/cuda
nvcc *.cu -o app
./app
```

## 🔧 常用命令

### 环境管理
```bash
./start.sh      # 启动环境
./stop.sh       # 停止环境
./enter.sh      # 进入环境
./logs.sh       # 查看日志
./rebuild.sh    # 重建环境
```

### 容器操作
```bash
# 查看运行状态
docker-compose ps

# 查看资源使用
docker stats ai-dev-environment

# 清理未使用的镜像
docker system prune -f
```

## 📁 目录结构

```
.
├── data/                    # 持久化数据
│   ├── models/             # AI模型存储
│   ├── datasets/           # 数据集存储
│   ├── checkpoints/        # 训练检查点
│   └── projects/           # 项目代码
├── workspace/              # 工作空间
├── docker-compose.yml      # Docker Compose配置
├── Dockerfile             # Docker镜像定义
├── .env                   # 环境变量配置
└── *.sh                   # 快捷脚本
```

## 🚨 故障排除

### GPU不可用
```bash
# 检查NVIDIA驱动
nvidia-smi

# 检查Docker GPU支持
docker run --rm --gpus all nvidia/cuda:11.0-base nvidia-smi
```

### 端口冲突
```bash
# 修改.env文件中的端口配置
# 然后重启环境
./stop.sh && ./start.sh
```

### 内存不足
```bash
# 修改docker-compose.yml中的内存限制
# 或者清理Docker缓存
docker system prune -a
```
EOF
    
    log_success "✅ 使用说明创建完成"
}

# 主函数
main() {
    echo "🚀 Python AI开发环境设置脚本"
    echo "================================"
    echo ""
    
    check_china_environment
    check_requirements
    create_directories
    create_env_file
    create_shortcuts
    create_readme
    
    echo ""
    log_success "🎉 环境设置完成！"
    echo ""
    echo "📋 下一步操作:"
    echo "  1. 修改 .env 文件中的配置 (可选)"
    echo "  2. 运行 ./start.sh 启动环境"
    echo "  3. 运行 ./enter.sh 进入开发环境"
    echo "  4. 查看 DOCKER_USAGE.md 了解详细使用方法"
    echo ""
    echo "🔗 快速启动:"
    echo "  ./start.sh && ./enter.sh"
}

# 执行主函数
main "$@"
