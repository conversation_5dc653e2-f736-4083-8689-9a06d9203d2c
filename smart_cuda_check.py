#!/usr/bin/env python3
"""
智能CUDA环境验证脚本
适用于构建时和运行时的CUDA环境检查
"""

import subprocess
import sys
import os

def run_command(cmd, description=""):
    """安全执行命令"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=30)
        return result.returncode == 0, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return False, "", f"命令超时: {cmd}"
    except Exception as e:
        return False, "", f"执行错误: {e}"

def check_cuda_compiler():
    """检查CUDA编译器"""
    print("🔍 检查CUDA编译器...")
    success, stdout, stderr = run_command("nvcc --version")
    
    if success:
        version_line = [line for line in stdout.split('\n') if 'release' in line.lower()]
        if version_line:
            print(f"✅ CUDA编译器: {version_line[0].strip()}")
            return True
    
    print(f"❌ CUDA编译器检查失败: {stderr}")
    return False

def check_cuda_libraries():
    """检查CUDA库文件"""
    print("🔍 检查CUDA库文件...")
    
    # 检查关键CUDA库
    cuda_libs = [
        "/usr/local/cuda*/lib64/libcudart.so*",
        "/usr/local/cuda*/lib64/libcublas.so*",
        "/usr/local/cuda*/lib64/libcurand.so*"
    ]
    
    found_libs = 0
    for lib_pattern in cuda_libs:
        success, stdout, _ = run_command(f"ls {lib_pattern} 2>/dev/null")
        if success and stdout.strip():
            found_libs += 1
            lib_name = lib_pattern.split('/')[-1].replace('*', '').replace('.so', '')
            print(f"✅ 找到库: {lib_name}")
    
    if found_libs >= 2:
        print(f"✅ CUDA库检查通过 ({found_libs}/{len(cuda_libs)})")
        return True
    else:
        print(f"⚠️ CUDA库不完整 ({found_libs}/{len(cuda_libs)})")
        return False

def check_nvidia_smi():
    """检查nvidia-smi (仅运行时)"""
    print("🔍 检查NVIDIA驱动...")
    success, stdout, stderr = run_command("nvidia-smi --query-gpu=name --format=csv,noheader")
    
    if success and stdout.strip():
        gpu_name = stdout.strip().split('\n')[0]
        print(f"✅ GPU检测到: {gpu_name}")
        return True
    else:
        print("⚠️ nvidia-smi不可用 (这在Docker构建时是正常的)")
        return False

def check_python_cuda():
    """检查Python CUDA支持"""
    print("🔍 检查Python CUDA支持...")
    
    python_check = '''
try:
    import torch
    print(f"PyTorch版本: {torch.__version__}")
    
    if torch.cuda.is_available():
        print(f"CUDA版本: {torch.version.cuda}")
        print(f"GPU数量: {torch.cuda.device_count()}")
        print("CUDA_AVAILABLE")
    else:
        print("CUDA_NOT_AVAILABLE")
except ImportError:
    print("PYTORCH_NOT_FOUND")
except Exception as e:
    print(f"ERROR: {e}")
'''
    
    success, stdout, stderr = run_command(f"python -c '{python_check}'")
    
    if success:
        if "CUDA_AVAILABLE" in stdout:
            print("✅ Python CUDA支持正常")
            return True
        elif "PYTORCH_NOT_FOUND" in stdout:
            print("⚠️ PyTorch未安装 (可能需要激活AI环境)")
            return False
        else:
            print("⚠️ Python CUDA支持有问题")
            return False
    else:
        print("⚠️ Python检查失败")
        return False

def main():
    print("🎯 智能CUDA环境验证")
    print("=" * 50)
    
    # 检测运行环境
    is_build_time = os.environ.get('DOCKER_BUILDKIT') or os.environ.get('BUILDKIT_FRONTEND')
    is_runtime = os.path.exists('/proc/driver/nvidia') or os.path.exists('/dev/nvidia0')
    
    if is_build_time:
        print("🏗️ 检测到构建时环境")
    elif is_runtime:
        print("🚀 检测到运行时环境")
    else:
        print("🔍 环境类型未知，执行通用检查")
    
    print()
    
    # 执行检查
    checks = []
    
    # 基础CUDA检查 (构建时和运行时都执行)
    checks.append(("CUDA编译器", check_cuda_compiler()))
    checks.append(("CUDA库文件", check_cuda_libraries()))
    
    # 运行时检查
    if not is_build_time:
        checks.append(("NVIDIA驱动", check_nvidia_smi()))
        checks.append(("Python CUDA", check_python_cuda()))
    
    # 总结结果
    print()
    print("📊 检查结果总结:")
    print("-" * 30)
    
    passed = sum(1 for _, result in checks if result)
    total = len(checks)
    
    for name, result in checks:
        status = "✅" if result else "❌"
        print(f"{status} {name}")
    
    print(f"\n通过率: {passed}/{total} ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 所有检查通过！")
        return 0
    elif passed >= total * 0.7:
        print("⚠️ 大部分检查通过，可能需要运行时验证")
        return 0
    else:
        print("❌ 多项检查失败，需要排查问题")
        return 1

if __name__ == "__main__":
    sys.exit(main())
