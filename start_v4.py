#!/usr/bin/env python3
"""
凤凰涅槃计划V4环境启动器 - 跨平台Python版本
自动检测操作系统并调用相应的启动脚本
支持 Windows、Linux、macOS
"""

import os
import sys
import platform
import subprocess
import argparse
from pathlib import Path

class V4Launcher:
    """V4环境启动器"""
    
    def __init__(self):
        self.system = platform.system().lower()
        self.script_dir = Path(__file__).parent
        
    def detect_environment(self):
        """检测运行环境"""
        print(f"🔍 检测到操作系统: {platform.system()} {platform.release()}")
        print(f"🐍 Python版本: {sys.version.split()[0]}")
        
        # 检测是否在WSL中运行
        if self.system == "linux" and "microsoft" in platform.release().lower():
            print("🐧 检测到WSL环境")
            return "wsl"
        
        return self.system
    
    def find_script(self, env_type):
        """查找合适的启动脚本"""
        scripts = {
            "windows": [
                "start_v4_environment.ps1",
                "start_v4_environment.bat"
            ],
            "linux": [
                "start_v4_environment.sh"
            ],
            "darwin": [  # macOS
                "start_v4_environment.sh"
            ],
            "wsl": [
                "start_v4_environment.sh"
            ]
        }
        
        for script_name in scripts.get(env_type, []):
            script_path = self.script_dir / script_name
            if script_path.exists():
                return script_path
        
        return None
    
    def check_dependencies(self):
        """检查基本依赖"""
        dependencies = ["docker"]
        missing = []
        
        for dep in dependencies:
            try:
                subprocess.run([dep, "--version"], 
                             capture_output=True, 
                             check=True, 
                             timeout=10)
                print(f"✅ {dep} 已安装")
            except (subprocess.CalledProcessError, FileNotFoundError, subprocess.TimeoutExpired):
                missing.append(dep)
                print(f"❌ {dep} 未安装或不可用")
        
        if missing:
            print(f"\n⚠️ 缺少依赖: {', '.join(missing)}")
            print("请先安装Docker Desktop")
            if self.system == "windows":
                print("下载地址: https://www.docker.com/products/docker-desktop")
            return False
        
        return True
    
    def run_powershell_script(self, script_path, action):
        """运行PowerShell脚本"""
        try:
            # 尝试使用PowerShell Core (pwsh)
            cmd = ["pwsh", "-ExecutionPolicy", "Bypass", "-File", str(script_path), action]
            result = subprocess.run(cmd, check=False)
            
            if result.returncode != 0:
                # 回退到Windows PowerShell
                print("🔄 尝试使用Windows PowerShell...")
                cmd = ["powershell", "-ExecutionPolicy", "Bypass", "-File", str(script_path), action]
                result = subprocess.run(cmd, check=False)
            
            return result.returncode == 0
            
        except FileNotFoundError:
            print("❌ PowerShell未找到，尝试使用批处理文件...")
            return False
    
    def run_batch_script(self, action):
        """运行批处理脚本"""
        batch_script = self.script_dir / "start_v4_environment.bat"
        if not batch_script.exists():
            print(f"❌ 批处理脚本不存在: {batch_script}")
            return False
        
        try:
            cmd = [str(batch_script), action]
            result = subprocess.run(cmd, check=False)
            return result.returncode == 0
        except Exception as e:
            print(f"❌ 执行批处理脚本失败: {e}")
            return False
    
    def run_bash_script(self, script_path, action):
        """运行Bash脚本"""
        try:
            # 确保脚本有执行权限
            os.chmod(script_path, 0o755)
            
            cmd = ["bash", str(script_path), action]
            result = subprocess.run(cmd, check=False)
            return result.returncode == 0
            
        except Exception as e:
            print(f"❌ 执行Bash脚本失败: {e}")
            return False
    
    def launch(self, action="start"):
        """启动V4环境"""
        print("🚀 凤凰涅槃计划V4环境启动器 (跨平台版)")
        print("=" * 50)
        
        # 检测环境
        env_type = self.detect_environment()
        
        # 检查依赖
        if not self.check_dependencies():
            return False
        
        # 查找脚本
        script_path = self.find_script(env_type)
        if not script_path:
            print(f"❌ 未找到适用于 {env_type} 的启动脚本")
            return False
        
        print(f"📜 使用脚本: {script_path.name}")
        print(f"🎯 执行操作: {action}")
        print("-" * 30)
        
        # 根据脚本类型执行
        success = False
        
        if script_path.suffix == ".ps1":
            # PowerShell脚本
            success = self.run_powershell_script(script_path, action)
            if not success and env_type == "windows":
                # PowerShell失败，尝试批处理
                print("🔄 PowerShell执行失败，尝试批处理文件...")
                success = self.run_batch_script(action)
                
        elif script_path.suffix == ".bat":
            # 批处理脚本
            success = self.run_batch_script(action)
            
        elif script_path.suffix == ".sh":
            # Bash脚本
            success = self.run_bash_script(script_path, action)
        
        if success:
            print("\n🎉 操作完成！")
            if action == "start":
                print("\n📚 快速访问:")
                print("  • Jupyter Lab: http://localhost:8888")
                print("  • MLflow UI:   http://localhost:5000")
                print("  • Grafana:     http://localhost:3000")
        else:
            print("\n❌ 操作失败，请检查错误信息")
        
        return success
    
    def show_help(self):
        """显示帮助信息"""
        print("🚀 凤凰涅槃计划V4环境启动器")
        print("\n用法: python start_v4.py [action]")
        print("\n可用操作:")
        print("  start   - 构建并启动完整的V4环境 (默认)")
        print("  build   - 仅构建V4镜像")
        print("  stop    - 停止所有V4服务")
        print("  restart - 重启V4服务")
        print("  status  - 查看服务状态")
        print("  logs    - 查看服务日志")
        print("  clean   - 清理所有V4数据和镜像")
        print("\n示例:")
        print("  python start_v4.py start")
        print("  python start_v4.py stop")
        print("  python start_v4.py logs")
        
        print(f"\n🖥️ 当前系统: {platform.system()}")
        print("📁 可用脚本:")
        
        for script_name in ["start_v4_environment.ps1", "start_v4_environment.bat", "start_v4_environment.sh"]:
            script_path = self.script_dir / script_name
            status = "✅" if script_path.exists() else "❌"
            print(f"  {status} {script_name}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="凤凰涅槃计划V4环境启动器",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    parser.add_argument(
        "action",
        nargs="?",
        default="start",
        choices=["start", "build", "stop", "restart", "status", "logs", "clean", "help"],
        help="要执行的操作 (默认: start)"
    )
    
    args = parser.parse_args()
    
    launcher = V4Launcher()
    
    if args.action == "help":
        launcher.show_help()
        return
    
    try:
        success = launcher.launch(args.action)
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 发生未预期的错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
