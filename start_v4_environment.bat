@echo off
REM 凤凰涅槃计划V4环境启动脚本 - Windows批处理版本
REM 适用于Windows命令提示符和PowerShell
REM 一键启动完整的企业级AI开发环境

setlocal enabledelayedexpansion

REM 设置代码页为UTF-8以支持中文显示
chcp 65001 >nul

REM 获取命令行参数
set "ACTION=%~1"
if "%ACTION%"=="" set "ACTION=start"

REM 颜色代码定义 (Windows 10+)
set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "MAGENTA=[95m"
set "CYAN=[96m"
set "WHITE=[97m"
set "RESET=[0m"

goto main

:print_header
echo %MAGENTA%================================%RESET%
echo %MAGENTA%%~1%RESET%
echo %MAGENTA%================================%RESET%
goto :eof

:print_success
echo %GREEN%✅ %~1%RESET%
goto :eof

:print_error
echo %RED%❌ %~1%RESET%
goto :eof

:print_warning
echo %YELLOW%⚠️ %~1%RESET%
goto :eof

:print_info
echo %BLUE%ℹ️ %~1%RESET%
goto :eof

:check_dependencies
call :print_header "检查系统依赖"

REM 检查Docker
docker --version >nul 2>&1
if errorlevel 1 (
    call :print_error "Docker未安装或未启动，请先安装Docker Desktop for Windows"
    call :print_info "下载地址: https://www.docker.com/products/docker-desktop"
    exit /b 1
) else (
    for /f "tokens=*" %%i in ('docker --version 2^>nul') do (
        call :print_success "Docker已安装: %%i"
    )
)

REM 检查Docker Compose
docker-compose --version >nul 2>&1
if errorlevel 1 (
    REM 尝试新版本的docker compose命令
    docker compose version >nul 2>&1
    if errorlevel 1 (
        call :print_error "Docker Compose未安装，请确保Docker Desktop包含Compose功能"
        exit /b 1
    ) else (
        for /f "tokens=*" %%i in ('docker compose version 2^>nul') do (
            call :print_success "Docker Compose已安装: %%i"
        )
        REM 设置别名（在批处理中通过doskey实现）
        doskey docker-compose=docker compose $*
    )
) else (
    for /f "tokens=*" %%i in ('docker-compose --version 2^>nul') do (
        call :print_success "Docker Compose已安装: %%i"
    )
)

REM 检查NVIDIA Docker支持（可选）
docker run --rm --gpus all nvidia/cuda:11.0-base nvidia-smi >nul 2>&1
if errorlevel 1 (
    call :print_warning "NVIDIA Docker支持未检测到，GPU功能可能不可用"
    call :print_info "如需GPU支持，请安装NVIDIA Container Toolkit"
) else (
    call :print_success "NVIDIA Docker支持已启用"
)

goto :eof

:create_directories
call :print_header "创建项目目录"

set "DIRECTORIES=workspace data models mlruns logs sql\init clickhouse\config monitoring monitoring\grafana\dashboards monitoring\grafana\datasources"

for %%d in (%DIRECTORIES%) do (
    if not exist "%%d" (
        mkdir "%%d" >nul 2>&1
        call :print_success "创建目录: %%d"
    ) else (
        call :print_info "目录已存在: %%d"
    )
)

goto :eof

:create_monitoring_configs
call :print_header "创建监控配置"

REM 创建Prometheus配置
if not exist "monitoring" mkdir "monitoring"

(
echo global:
echo   scrape_interval: 15s
echo   evaluation_interval: 15s
echo.
echo rule_files:
echo   # - "first_rules.yml"
echo   # - "second_rules.yml"
echo.
echo scrape_configs:
echo   - job_name: 'prometheus'
echo     static_configs:
echo       - targets: ['localhost:9090']
echo.
echo   - job_name: 'phoenix-v4'
echo     static_configs:
echo       - targets: ['phoenix-v4:8888']
echo     metrics_path: '/metrics'
echo     scrape_interval: 30s
) > monitoring\prometheus.yml

call :print_success "创建Prometheus配置"

REM 创建Grafana数据源配置
if not exist "monitoring\grafana\datasources" mkdir "monitoring\grafana\datasources"

(
echo apiVersion: 1
echo.
echo datasources:
echo   - name: Prometheus
echo     type: prometheus
echo     access: proxy
echo     url: http://prometheus:9090
echo     isDefault: true
) > monitoring\grafana\datasources\prometheus.yml

call :print_success "创建Grafana数据源配置"

goto :eof

:build_v4_image
call :print_header "构建凤凰涅槃计划V4镜像"

if not exist "Dockerfile.robust" (
    call :print_error "Dockerfile.robust文件不存在"
    exit /b 1
)

call :print_info "开始构建V4镜像，这可能需要20-30分钟..."

docker build -f Dockerfile.robust -t phoenix-v4-expert:latest .
if errorlevel 1 (
    call :print_error "V4镜像构建失败"
    exit /b 1
) else (
    call :print_success "V4镜像构建完成"
)

goto :eof

:start_services
call :print_header "启动V4企业级服务"

call :print_info "启动数据库和监控服务..."
docker-compose -f docker-compose.v4.yml up -d postgres redis clickhouse neo4j prometheus grafana jaeger zookeeper kafka

if errorlevel 1 (
    call :print_error "启动基础服务失败"
    exit /b 1
)

call :print_info "等待数据库服务启动..."
timeout /t 30 /nobreak >nul

call :print_info "启动主要AI开发环境..."
docker-compose -f docker-compose.v4.yml up -d phoenix-v4

if errorlevel 1 (
    call :print_error "启动AI环境失败"
    exit /b 1
) else (
    call :print_success "所有服务启动完成"
)

goto :eof

:verify_environment
call :print_header "验证V4环境"

call :print_info "等待所有服务完全启动..."
timeout /t 60 /nobreak >nul

call :print_info "检查服务状态..."
docker-compose -f docker-compose.v4.yml ps

if exist "verify_v4_environment.py" (
    call :print_info "运行环境验证脚本..."
    docker exec phoenix-v4-main python /workspace/project/verify_v4_environment.py
    if errorlevel 1 (
        call :print_warning "环境验证脚本执行失败，请手动检查环境"
    )
) else (
    call :print_warning "环境验证脚本不存在，跳过自动验证"
)

goto :eof

:show_access_info
call :print_header "V4环境访问信息"

echo %CYAN%🚀 凤凰涅槃计划V4企业级AI开发环境已启动！%RESET%
echo.
echo %GREEN%📊 Web界面访问地址:%RESET%
echo   • Jupyter Lab:    http://localhost:8888
echo   • MLflow UI:      http://localhost:5000
echo   • Grafana:        http://localhost:3000 (admin/phoenix_v4_2024)
echo   • Prometheus:     http://localhost:9090
echo   • Jaeger UI:      http://localhost:16686
echo   • Neo4j Browser:  http://localhost:7474 (phoenix/phoenix_v4_2024)
echo.
echo %GREEN%🗄️ 数据库连接信息:%RESET%
echo   • PostgreSQL:     localhost:5432 (phoenix/phoenix_v4_2024)
echo   • Redis:          localhost:6379
echo   • ClickHouse:     localhost:9000 (phoenix/phoenix_v4_2024)
echo   • Neo4j:          localhost:7687 (phoenix/phoenix_v4_2024)
echo   • Kafka:          localhost:9092
echo.
echo %GREEN%🔧 常用命令:%RESET%
echo   • 进入AI环境:     docker exec -it phoenix-v4-main bash
echo   • 激活AI环境:     source /opt/miniconda/bin/activate ai
echo   • 查看GPU状态:    docker exec phoenix-v4-main nvidia-smi
echo   • 停止所有服务:   start_v4_environment.bat stop
echo   • 查看日志:       start_v4_environment.bat logs
echo.
echo %YELLOW%📚 快速开始:%RESET%
echo   1. 访问 Jupyter Lab: http://localhost:8888
echo   2. 创建新的Python笔记本
echo   3. 导入V4新增的AI框架: import jax, mlflow, transformers
echo   4. 开始您的企业级AI项目开发！
echo.

goto :eof

:main
call :print_header "🚀 凤凰涅槃计划V4环境启动器 (Windows版)"

if "%ACTION%"=="build" (
    call :check_dependencies
    call :create_directories
    call :create_monitoring_configs
    call :build_v4_image
) else if "%ACTION%"=="start" (
    call :check_dependencies
    call :create_directories
    call :create_monitoring_configs
    call :build_v4_image
    call :start_services
    call :verify_environment
    call :show_access_info
) else if "%ACTION%"=="stop" (
    call :print_info "停止V4环境..."
    docker-compose -f docker-compose.v4.yml down
    if not errorlevel 1 (
        call :print_success "V4环境已停止"
    )
) else if "%ACTION%"=="restart" (
    call :print_info "重启V4环境..."
    docker-compose -f docker-compose.v4.yml restart
    if not errorlevel 1 (
        call :print_success "V4环境已重启"
    )
) else if "%ACTION%"=="status" (
    call :print_info "V4环境状态:"
    docker-compose -f docker-compose.v4.yml ps
) else if "%ACTION%"=="logs" (
    docker-compose -f docker-compose.v4.yml logs -f
) else if "%ACTION%"=="clean" (
    set /p "confirmation=这将删除所有V4数据，确认请输入 'yes': "
    if "!confirmation!"=="yes" (
        docker-compose -f docker-compose.v4.yml down -v
        docker system prune -f
        call :print_success "V4环境已清理"
    ) else (
        call :print_info "取消清理操作"
    )
) else (
    echo 用法: start_v4_environment.bat [start^|build^|stop^|restart^|status^|logs^|clean]
    echo.
    echo 命令说明:
    echo   start   - 构建并启动完整的V4环境 (默认)
    echo   build   - 仅构建V4镜像
    echo   stop    - 停止所有V4服务
    echo   restart - 重启V4服务
    echo   status  - 查看服务状态
    echo   logs    - 查看服务日志
    echo   clean   - 清理所有V4数据和镜像
    exit /b 1
)

endlocal
