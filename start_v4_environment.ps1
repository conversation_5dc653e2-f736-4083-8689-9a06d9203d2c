# 凤凰涅槃计划V4环境启动脚本 - PowerShell版本
# 适用于Windows PowerShell和PowerShell Core
# 一键启动完整的企业级AI开发环境

param(
    [Parameter(Position=0)]
    [ValidateSet("start", "build", "stop", "restart", "status", "logs", "clean")]
    [string]$Action = "start"
)

# 设置错误处理
$ErrorActionPreference = "Stop"

# 颜色定义
$Colors = @{
    Red = "Red"
    Green = "Green"
    Yellow = "Yellow"
    Blue = "Blue"
    Magenta = "Magenta"
    Cyan = "Cyan"
    White = "White"
}

# 打印带颜色的消息
function Write-ColorMessage {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

function Write-Header {
    param([string]$Title)
    Write-ColorMessage "================================" "Magenta"
    Write-ColorMessage $Title "Magenta"
    Write-ColorMessage "================================" "Magenta"
}

function Write-Success {
    param([string]$Message)
    Write-ColorMessage "✅ $Message" "Green"
}

function Write-Error {
    param([string]$Message)
    Write-ColorMessage "❌ $Message" "Red"
}

function Write-Warning {
    param([string]$Message)
    Write-ColorMessage "⚠️ $Message" "Yellow"
}

function Write-Info {
    param([string]$Message)
    Write-ColorMessage "ℹ️ $Message" "Blue"
}

# 检查系统依赖
function Test-Dependencies {
    Write-Header "检查系统依赖"
    
    # 检查Docker
    try {
        $dockerVersion = docker --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Docker已安装: $dockerVersion"
        } else {
            throw "Docker命令执行失败"
        }
    } catch {
        Write-Error "Docker未安装或未启动，请先安装Docker Desktop for Windows"
        Write-Info "下载地址: https://www.docker.com/products/docker-desktop"
        exit 1
    }
    
    # 检查Docker Compose
    try {
        $composeVersion = docker-compose --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Docker Compose已安装: $composeVersion"
        } else {
            # 尝试使用docker compose (新版本)
            $composeVersion = docker compose version 2>$null
            if ($LASTEXITCODE -eq 0) {
                Write-Success "Docker Compose已安装: $composeVersion"
                # 创建docker-compose别名
                Set-Alias -Name docker-compose -Value "docker compose" -Scope Global
            } else {
                throw "Docker Compose命令执行失败"
            }
        }
    } catch {
        Write-Error "Docker Compose未安装，请确保Docker Desktop包含Compose功能"
        exit 1
    }
    
    # 检查NVIDIA Docker支持 (可选)
    try {
        $gpuTest = docker run --rm --gpus all nvidia/cuda:11.0-base nvidia-smi 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Success "NVIDIA Docker支持已启用"
        } else {
            Write-Warning "NVIDIA Docker支持未检测到，GPU功能可能不可用"
            Write-Info "如需GPU支持，请安装NVIDIA Container Toolkit"
        }
    } catch {
        Write-Warning "无法检测NVIDIA Docker支持，GPU功能可能不可用"
    }
}

# 创建必要的目录
function New-ProjectDirectories {
    Write-Header "创建项目目录"
    
    $directories = @(
        "workspace",
        "data",
        "models",
        "mlruns",
        "logs",
        "sql\init",
        "clickhouse\config",
        "monitoring",
        "monitoring\grafana\dashboards",
        "monitoring\grafana\datasources"
    )
    
    foreach ($dir in $directories) {
        $fullPath = Join-Path $PWD $dir
        if (-not (Test-Path $fullPath)) {
            New-Item -ItemType Directory -Path $fullPath -Force | Out-Null
            Write-Success "创建目录: $dir"
        } else {
            Write-Info "目录已存在: $dir"
        }
    }
}

# 创建监控配置文件
function New-MonitoringConfigs {
    Write-Header "创建监控配置"
    
    # 确保监控目录存在
    $monitoringDir = Join-Path $PWD "monitoring"
    if (-not (Test-Path $monitoringDir)) {
        New-Item -ItemType Directory -Path $monitoringDir -Force | Out-Null
    }
    
    # Prometheus配置
    $prometheusConfig = @"
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
  
  - job_name: 'phoenix-v4'
    static_configs:
      - targets: ['phoenix-v4:8888']
    metrics_path: '/metrics'
    scrape_interval: 30s
"@
    
    $prometheusPath = Join-Path $monitoringDir "prometheus.yml"
    $prometheusConfig | Out-File -FilePath $prometheusPath -Encoding UTF8
    Write-Success "创建Prometheus配置"
    
    # Grafana数据源配置
    $grafanaDir = Join-Path $monitoringDir "grafana\datasources"
    if (-not (Test-Path $grafanaDir)) {
        New-Item -ItemType Directory -Path $grafanaDir -Force | Out-Null
    }
    
    $grafanaConfig = @"
apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
"@
    
    $grafanaPath = Join-Path $grafanaDir "prometheus.yml"
    $grafanaConfig | Out-File -FilePath $grafanaPath -Encoding UTF8
    Write-Success "创建Grafana数据源配置"
}

# 构建V4镜像
function Build-V4Image {
    Write-Header "构建凤凰涅槃计划V4镜像"
    
    $dockerfilePath = Join-Path $PWD "Dockerfile.robust"
    if (-not (Test-Path $dockerfilePath)) {
        Write-Error "Dockerfile.robust文件不存在"
        exit 1
    }
    
    Write-Info "开始构建V4镜像，这可能需要20-30分钟..."
    
    # 构建镜像
    try {
        docker build -f Dockerfile.robust -t phoenix-v4-expert:latest .
        if ($LASTEXITCODE -eq 0) {
            Write-Success "V4镜像构建完成"
        } else {
            throw "构建失败"
        }
    } catch {
        Write-Error "V4镜像构建失败"
        exit 1
    }
}

# 启动服务
function Start-Services {
    Write-Header "启动V4企业级服务"
    
    # 启动数据库和监控服务
    Write-Info "启动数据库和监控服务..."
    docker-compose -f docker-compose.v4.yml up -d postgres redis clickhouse neo4j prometheus grafana jaeger zookeeper kafka
    
    if ($LASTEXITCODE -ne 0) {
        Write-Error "启动基础服务失败"
        exit 1
    }
    
    # 等待数据库启动
    Write-Info "等待数据库服务启动..."
    Start-Sleep -Seconds 30
    
    # 启动主要的AI环境
    Write-Info "启动主要AI开发环境..."
    docker-compose -f docker-compose.v4.yml up -d phoenix-v4
    
    if ($LASTEXITCODE -eq 0) {
        Write-Success "所有服务启动完成"
    } else {
        Write-Error "启动AI环境失败"
        exit 1
    }
}

# 验证环境
function Test-Environment {
    Write-Header "验证V4环境"
    
    Write-Info "等待所有服务完全启动..."
    Start-Sleep -Seconds 60
    
    # 检查服务状态
    Write-Info "检查服务状态..."
    docker-compose -f docker-compose.v4.yml ps
    
    # 运行环境验证脚本
    $verifyScript = Join-Path $PWD "verify_v4_environment.py"
    if (Test-Path $verifyScript) {
        Write-Info "运行环境验证脚本..."
        try {
            docker exec phoenix-v4-main python /workspace/project/verify_v4_environment.py
        } catch {
            Write-Warning "环境验证脚本执行失败，请手动检查环境"
        }
    } else {
        Write-Warning "环境验证脚本不存在，跳过自动验证"
    }
}

# 显示访问信息
function Show-AccessInfo {
    Write-Header "V4环境访问信息"
    
    Write-ColorMessage "🚀 凤凰涅槃计划V4企业级AI开发环境已启动！" "Cyan"
    Write-Host ""
    Write-ColorMessage "📊 Web界面访问地址:" "Green"
    Write-Host "  • Jupyter Lab:    http://localhost:8888"
    Write-Host "  • MLflow UI:      http://localhost:5000"
    Write-Host "  • Grafana:        http://localhost:3000 (admin/phoenix_v4_2024)"
    Write-Host "  • Prometheus:     http://localhost:9090"
    Write-Host "  • Jaeger UI:      http://localhost:16686"
    Write-Host "  • Neo4j Browser:  http://localhost:7474 (phoenix/phoenix_v4_2024)"
    Write-Host ""
    Write-ColorMessage "🗄️ 数据库连接信息:" "Green"
    Write-Host "  • PostgreSQL:     localhost:5432 (phoenix/phoenix_v4_2024)"
    Write-Host "  • Redis:          localhost:6379"
    Write-Host "  • ClickHouse:     localhost:9000 (phoenix/phoenix_v4_2024)"
    Write-Host "  • Neo4j:          localhost:7687 (phoenix/phoenix_v4_2024)"
    Write-Host "  • Kafka:          localhost:9092"
    Write-Host ""
    Write-ColorMessage "🔧 常用命令:" "Green"
    Write-Host "  • 进入AI环境:     docker exec -it phoenix-v4-main bash"
    Write-Host "  • 激活AI环境:     source /opt/miniconda/bin/activate ai"
    Write-Host "  • 查看GPU状态:    docker exec phoenix-v4-main nvidia-smi"
    Write-Host "  • 停止所有服务:   .\start_v4_environment.ps1 stop"
    Write-Host "  • 查看日志:       .\start_v4_environment.ps1 logs"
    Write-Host ""
    Write-ColorMessage "📚 快速开始:" "Yellow"
    Write-Host "  1. 访问 Jupyter Lab: http://localhost:8888"
    Write-Host "  2. 创建新的Python笔记本"
    Write-Host "  3. 导入V4新增的AI框架: import jax, mlflow, transformers"
    Write-Host "  4. 开始您的企业级AI项目开发！"
    Write-Host ""
}

# 主函数
function Main {
    Write-Header "🚀 凤凰涅槃计划V4环境启动器 (Windows版)"
    
    switch ($Action) {
        "build" {
            Test-Dependencies
            New-ProjectDirectories
            New-MonitoringConfigs
            Build-V4Image
        }
        "start" {
            Test-Dependencies
            New-ProjectDirectories
            New-MonitoringConfigs
            Build-V4Image
            Start-Services
            Test-Environment
            Show-AccessInfo
        }
        "stop" {
            Write-Info "停止V4环境..."
            docker-compose -f docker-compose.v4.yml down
            if ($LASTEXITCODE -eq 0) {
                Write-Success "V4环境已停止"
            }
        }
        "restart" {
            Write-Info "重启V4环境..."
            docker-compose -f docker-compose.v4.yml restart
            if ($LASTEXITCODE -eq 0) {
                Write-Success "V4环境已重启"
            }
        }
        "status" {
            Write-Info "V4环境状态:"
            docker-compose -f docker-compose.v4.yml ps
        }
        "logs" {
            docker-compose -f docker-compose.v4.yml logs -f
        }
        "clean" {
            $confirmation = Read-Host "这将删除所有V4数据，确认请输入 'yes'"
            if ($confirmation -eq "yes") {
                docker-compose -f docker-compose.v4.yml down -v
                docker system prune -f
                Write-Success "V4环境已清理"
            } else {
                Write-Info "取消清理操作"
            }
        }
        default {
            Write-Host "用法: .\start_v4_environment.ps1 [start|build|stop|restart|status|logs|clean]"
            Write-Host ""
            Write-Host "命令说明:"
            Write-Host "  start   - 构建并启动完整的V4环境 (默认)"
            Write-Host "  build   - 仅构建V4镜像"
            Write-Host "  stop    - 停止所有V4服务"
            Write-Host "  restart - 重启V4服务"
            Write-Host "  status  - 查看服务状态"
            Write-Host "  logs    - 查看服务日志"
            Write-Host "  clean   - 清理所有V4数据和镜像"
            exit 1
        }
    }
}

# 执行主函数
try {
    Main
} catch {
    Write-Error "执行过程中发生错误: $($_.Exception.Message)"
    exit 1
}
