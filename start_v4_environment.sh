#!/bin/bash

# 凤凰涅槃计划V4环境启动脚本
# 一键启动完整的企业级AI开发环境

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    echo -e "${2}${1}${NC}"
}

print_header() {
    echo -e "${PURPLE}================================${NC}"
    echo -e "${PURPLE}$1${NC}"
    echo -e "${PURPLE}================================${NC}"
}

print_success() {
    print_message "✅ $1" $GREEN
}

print_error() {
    print_message "❌ $1" $RED
}

print_warning() {
    print_message "⚠️ $1" $YELLOW
}

print_info() {
    print_message "ℹ️ $1" $BLUE
}

# 检查依赖
check_dependencies() {
    print_header "检查系统依赖"
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    print_success "Docker已安装"
    
    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    print_success "Docker Compose已安装"
    
    # 检查NVIDIA Docker支持
    if ! docker run --rm --gpus all nvidia/cuda:11.0-base nvidia-smi &> /dev/null; then
        print_warning "NVIDIA Docker支持未检测到，GPU功能可能不可用"
    else
        print_success "NVIDIA Docker支持已启用"
    fi
}

# 创建必要的目录
create_directories() {
    print_header "创建项目目录"
    
    directories=(
        "workspace"
        "data"
        "models"
        "mlruns"
        "logs"
        "sql/init"
        "clickhouse/config"
        "monitoring"
        "monitoring/grafana/dashboards"
        "monitoring/grafana/datasources"
    )
    
    for dir in "${directories[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            print_success "创建目录: $dir"
        else
            print_info "目录已存在: $dir"
        fi
    done
}

# 创建监控配置文件
create_monitoring_configs() {
    print_header "创建监控配置"
    
    # Prometheus配置
    cat > monitoring/prometheus.yml << 'EOF'
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
  
  - job_name: 'phoenix-v4'
    static_configs:
      - targets: ['phoenix-v4:8888']
    metrics_path: '/metrics'
    scrape_interval: 30s
EOF
    print_success "创建Prometheus配置"
    
    # Grafana数据源配置
    cat > monitoring/grafana/datasources/prometheus.yml << 'EOF'
apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
EOF
    print_success "创建Grafana数据源配置"
}

# 构建V4镜像
build_v4_image() {
    print_header "构建凤凰涅槃计划V4镜像"
    
    if [ ! -f "Dockerfile.robust" ]; then
        print_error "Dockerfile.robust文件不存在"
        exit 1
    fi
    
    print_info "开始构建V4镜像，这可能需要20-30分钟..."
    
    # 构建镜像
    if docker build -f Dockerfile.robust -t phoenix-v4-expert:latest .; then
        print_success "V4镜像构建完成"
    else
        print_error "V4镜像构建失败"
        exit 1
    fi
}

# 启动服务
start_services() {
    print_header "启动V4企业级服务"
    
    # 启动所有服务
    print_info "启动数据库和监控服务..."
    docker-compose -f docker-compose.v4.yml up -d postgres redis clickhouse neo4j prometheus grafana jaeger zookeeper kafka
    
    # 等待数据库启动
    print_info "等待数据库服务启动..."
    sleep 30
    
    # 启动主要的AI环境
    print_info "启动主要AI开发环境..."
    docker-compose -f docker-compose.v4.yml up -d phoenix-v4
    
    print_success "所有服务启动完成"
}

# 验证环境
verify_environment() {
    print_header "验证V4环境"
    
    print_info "等待所有服务完全启动..."
    sleep 60
    
    # 检查服务状态
    print_info "检查服务状态..."
    docker-compose -f docker-compose.v4.yml ps
    
    # 运行环境验证脚本
    if [ -f "verify_v4_environment.py" ]; then
        print_info "运行环境验证脚本..."
        docker exec phoenix-v4-main python /workspace/project/verify_v4_environment.py
    else
        print_warning "环境验证脚本不存在，跳过自动验证"
    fi
}

# 显示访问信息
show_access_info() {
    print_header "V4环境访问信息"
    
    echo -e "${CYAN}🚀 凤凰涅槃计划V4企业级AI开发环境已启动！${NC}"
    echo ""
    echo -e "${GREEN}📊 Web界面访问地址:${NC}"
    echo -e "  • Jupyter Lab:    http://localhost:8888"
    echo -e "  • MLflow UI:      http://localhost:5000"
    echo -e "  • Grafana:        http://localhost:3000 (admin/phoenix_v4_2024)"
    echo -e "  • Prometheus:     http://localhost:9090"
    echo -e "  • Jaeger UI:      http://localhost:16686"
    echo -e "  • Neo4j Browser:  http://localhost:7474 (phoenix/phoenix_v4_2024)"
    echo ""
    echo -e "${GREEN}🗄️ 数据库连接信息:${NC}"
    echo -e "  • PostgreSQL:     localhost:5432 (phoenix/phoenix_v4_2024)"
    echo -e "  • Redis:          localhost:6379"
    echo -e "  • ClickHouse:     localhost:9000 (phoenix/phoenix_v4_2024)"
    echo -e "  • Neo4j:          localhost:7687 (phoenix/phoenix_v4_2024)"
    echo -e "  • Kafka:          localhost:9092"
    echo ""
    echo -e "${GREEN}🔧 常用命令:${NC}"
    echo -e "  • 进入AI环境:     docker exec -it phoenix-v4-main bash"
    echo -e "  • 激活AI环境:     source /opt/miniconda/bin/activate ai"
    echo -e "  • 查看GPU状态:    docker exec phoenix-v4-main nvidia-smi"
    echo -e "  • 停止所有服务:   docker-compose -f docker-compose.v4.yml down"
    echo -e "  • 查看日志:       docker-compose -f docker-compose.v4.yml logs -f"
    echo ""
    echo -e "${YELLOW}📚 快速开始:${NC}"
    echo -e "  1. 访问 Jupyter Lab: http://localhost:8888"
    echo -e "  2. 创建新的Python笔记本"
    echo -e "  3. 导入V4新增的AI框架: import jax, mlflow, transformers"
    echo -e "  4. 开始您的企业级AI项目开发！"
    echo ""
}

# 主函数
main() {
    print_header "🚀 凤凰涅槃计划V4环境启动器"
    
    # 检查参数
    case "${1:-start}" in
        "build")
            check_dependencies
            create_directories
            create_monitoring_configs
            build_v4_image
            ;;
        "start")
            check_dependencies
            create_directories
            create_monitoring_configs
            build_v4_image
            start_services
            verify_environment
            show_access_info
            ;;
        "stop")
            print_info "停止V4环境..."
            docker-compose -f docker-compose.v4.yml down
            print_success "V4环境已停止"
            ;;
        "restart")
            print_info "重启V4环境..."
            docker-compose -f docker-compose.v4.yml restart
            print_success "V4环境已重启"
            ;;
        "status")
            print_info "V4环境状态:"
            docker-compose -f docker-compose.v4.yml ps
            ;;
        "logs")
            docker-compose -f docker-compose.v4.yml logs -f
            ;;
        "clean")
            print_warning "这将删除所有V4数据，确认请输入 'yes':"
            read -r confirmation
            if [ "$confirmation" = "yes" ]; then
                docker-compose -f docker-compose.v4.yml down -v
                docker system prune -f
                print_success "V4环境已清理"
            else
                print_info "取消清理操作"
            fi
            ;;
        *)
            echo "用法: $0 {start|build|stop|restart|status|logs|clean}"
            echo ""
            echo "命令说明:"
            echo "  start   - 构建并启动完整的V4环境 (默认)"
            echo "  build   - 仅构建V4镜像"
            echo "  stop    - 停止所有V4服务"
            echo "  restart - 重启V4服务"
            echo "  status  - 查看服务状态"
            echo "  logs    - 查看服务日志"
            echo "  clean   - 清理所有V4数据和镜像"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
