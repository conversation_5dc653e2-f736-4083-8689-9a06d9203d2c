#!/bin/bash

# 测试conda服务条款修复脚本
echo "🧪 测试conda服务条款修复..."

# 构建测试阶段
echo "🔨 构建Python开发阶段..."
docker build --target python-dev -t test-conda-fix -f Dockerfile.robust . 2>&1 | tee conda-test.log

# 检查构建结果
if [ $? -eq 0 ]; then
    echo "✅ conda修复成功！"
    
    # 运行容器测试conda环境
    echo "🔍 测试conda环境..."
    docker run --rm test-conda-fix /bin/bash -c "
        source /opt/miniconda/bin/activate llm_dev && 
        echo '✅ conda环境激活成功' && 
        python --version && 
        conda list | head -10
    "
else
    echo "❌ conda修复失败，查看详细日志:"
    echo "📋 错误日志摘要:"
    grep -i "error\|failed\|terms of service" conda-test.log | tail -10
fi