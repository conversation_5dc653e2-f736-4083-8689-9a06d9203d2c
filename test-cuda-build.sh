#!/bin/bash

# CUDA环境构建测试脚本
# 用于验证修复后的Dockerfile是否能正确构建

set -e

# 颜色定义
BLUE='\033[36m'
GREEN='\033[32m'
YELLOW='\033[33m'
RED='\033[31m'
NC='\033[0m'

echo -e "${BLUE}🔧 CUDA环境构建测试${NC}"
echo "=================================="
echo ""

# 检查Docker是否可用
if ! command -v docker &> /dev/null; then
    echo -e "${RED}❌ Docker未安装或不可用${NC}"
    echo "请先安装Docker后再运行此脚本"
    exit 1
fi

echo -e "${GREEN}✅ Docker环境检查通过${NC}"
echo ""

# 创建测试用的简化Dockerfile
echo -e "${YELLOW}📋 创建CUDA依赖测试Dockerfile...${NC}"

cat > Dockerfile.cuda-test << 'EOF'
# CUDA依赖测试Dockerfile
FROM nvidia/cuda:12.1.1-cudnn8-devel-ubuntu22.04

# 设置环境变量
ENV DEBIAN_FRONTEND=noninteractive

# 更新包管理器并安装基础工具
RUN apt-get update && apt-get install -y \
        wget curl git vim \
        build-essential cmake ninja-build \
        pkg-config \
    && rm -rf /var/lib/apt/lists/*

# 测试CUDA开发工具安装 (使用修复后的策略)
RUN apt-get update && \
    # 安装CUDA开发工具，避免版本冲突
    apt-get install -y --no-install-recommends \
        cuda-nvcc-12-1 \
        cuda-nvtx-12-1 \
        libcublas-dev-12-1 \
        libcurand-dev-12-1 \
        libcufft-dev-12-1 \
        libcusparse-dev-12-1 \
        libcusolver-dev-12-1 \
        cuda-profiler-api-12-1 \
        cuda-gdb-12-1 \
    && apt-get autoremove -y \
    && apt-get autoclean \
    && rm -rf /var/lib/apt/lists/*

# 验证cuDNN (使用基础镜像提供的版本)
RUN echo "验证cuDNN安装..." && \
    ldconfig && \
    find /usr -name "libcudnn*" -type f 2>/dev/null | head -5

# 安装TensorRT运行时
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        libnvinfer8 \
        libnvinfer-plugin8 \
        libnvonnxparsers8 \
        libnvparsers8 \
    && apt-get autoremove -y \
    && apt-get autoclean \
    && rm -rf /var/lib/apt/lists/*

# 创建测试脚本
RUN echo '#!/bin/bash' > /test-cuda.sh && \
    echo 'echo "🔍 CUDA环境验证"' >> /test-cuda.sh && \
    echo 'echo "=================="' >> /test-cuda.sh && \
    echo 'echo "NVCC版本:"' >> /test-cuda.sh && \
    echo 'nvcc --version' >> /test-cuda.sh && \
    echo 'echo ""' >> /test-cuda.sh && \
    echo 'echo "CUDA库检查:"' >> /test-cuda.sh && \
    echo 'find /usr/local/cuda -name "*.so" | head -5' >> /test-cuda.sh && \
    echo 'echo ""' >> /test-cuda.sh && \
    echo 'echo "cuDNN库检查:"' >> /test-cuda.sh && \
    echo 'find /usr -name "libcudnn*" | head -3' >> /test-cuda.sh && \
    echo 'echo ""' >> /test-cuda.sh && \
    echo 'echo "TensorRT库检查:"' >> /test-cuda.sh && \
    echo 'find /usr -name "libnvinfer*" | head -3' >> /test-cuda.sh && \
    chmod +x /test-cuda.sh

CMD ["/test-cuda.sh"]
EOF

echo -e "${GREEN}✅ 测试Dockerfile创建完成${NC}"
echo ""

# 构建测试镜像
echo -e "${YELLOW}🔨 开始构建CUDA测试镜像...${NC}"
echo "这可能需要几分钟时间..."
echo ""

if docker build -f Dockerfile.cuda-test -t cuda-test:latest . --progress=plain; then
    echo ""
    echo -e "${GREEN}🎉 CUDA测试镜像构建成功！${NC}"
    echo ""
    
    # 运行测试
    echo -e "${YELLOW}🧪 运行CUDA环境测试...${NC}"
    if docker run --rm cuda-test:latest; then
        echo ""
        echo -e "${GREEN}✅ CUDA环境测试通过！${NC}"
        echo -e "${GREEN}✅ 修复后的Dockerfile应该可以正常构建${NC}"
    else
        echo ""
        echo -e "${RED}❌ CUDA环境测试失败${NC}"
    fi
    
    # 清理测试镜像
    echo ""
    echo -e "${YELLOW}🧹 清理测试镜像...${NC}"
    docker rmi cuda-test:latest
    
else
    echo ""
    echo -e "${RED}❌ CUDA测试镜像构建失败${NC}"
    echo "请检查错误信息并调整Dockerfile配置"
fi

# 清理测试文件
rm -f Dockerfile.cuda-test

echo ""
echo -e "${BLUE}🔧 CUDA环境测试完成${NC}"
echo "=================================="

if [ $? -eq 0 ]; then
    echo ""
    echo -e "${GREEN}📋 下一步建议：${NC}"
    echo "1. 现在可以安全地构建完整的多语言开发环境："
    echo "   docker build -t multi-lang:latest ."
    echo ""
    echo "2. 如果仍有问题，可以考虑以下替代方案："
    echo "   - 使用不同的CUDA基础镜像版本"
    echo "   - 通过pip安装TensorRT而不是apt"
    echo "   - 跳过某些可选的CUDA组件"
fi
