#!/bin/bash

# CUDA/cuDNN修复效果测试脚本

set -e

# 颜色定义
BLUE='\033[36m'
GREEN='\033[32m'
YELLOW='\033[33m'
RED='\033[31m'
NC='\033[0m'

echo -e "${BLUE}🧪 CUDA/cuDNN修复效果测试${NC}"
echo "=================================="
echo ""

# 检查修复脚本
if [ ! -f "fix-cudnn-conflict.sh" ]; then
    echo -e "${RED}❌ 找不到修复脚本 fix-cudnn-conflict.sh${NC}"
    exit 1
fi

# 检查Dockerfile
dockerfiles=("Dockerfile.robust" "Dockerfile.no-conflict")
for dockerfile in "${dockerfiles[@]}"; do
    if [ -f "$dockerfile" ]; then
        echo -e "${GREEN}✅${NC} 找到 $dockerfile"
    else
        echo -e "${YELLOW}⚠️${NC} 未找到 $dockerfile"
    fi
done

echo ""

# 显示测试选项
echo -e "${YELLOW}📋 测试选项：${NC}"
echo "1. 测试修复脚本"
echo "2. 构建无冲突版本Docker镜像"
echo "3. 构建容错增强版Docker镜像"
echo "4. 快速CUDA环境验证"
echo "5. 生成修复报告"
echo ""

read -p "请选择测试选项 (1-5): " choice

case $choice in
    1)
        echo -e "${YELLOW}🔧 测试修复脚本...${NC}"
        echo ""
        
        # 给修复脚本执行权限
        chmod +x fix-cudnn-conflict.sh
        
        # 运行修复脚本的验证模式
        echo "运行修复脚本验证模式..."
        echo "5" | ./fix-cudnn-conflict.sh
        ;;
        
    2)
        echo -e "${YELLOW}🐳 构建无冲突版本Docker镜像...${NC}"
        echo ""
        
        if [ ! -f "Dockerfile.no-conflict" ]; then
            echo -e "${RED}❌ Dockerfile.no-conflict 不存在${NC}"
            exit 1
        fi
        
        echo "开始构建无冲突版本..."
        if docker build -f Dockerfile.no-conflict -t phoenix-v3-no-conflict:latest . --progress=plain; then
            echo ""
            echo -e "${GREEN}✅ 无冲突版本构建成功${NC}"
            
            # 运行验证
            echo -e "${YELLOW}🧪 运行环境验证...${NC}"
            docker run --rm --gpus all phoenix-v3-no-conflict:latest python /workspace/verify_environment.py
            
        else
            echo ""
            echo -e "${RED}❌ 无冲突版本构建失败${NC}"
        fi
        ;;
        
    3)
        echo -e "${YELLOW}🐳 构建容错增强版Docker镜像...${NC}"
        echo ""
        
        if [ ! -f "Dockerfile.robust" ]; then
            echo -e "${RED}❌ Dockerfile.robust 不存在${NC}"
            exit 1
        fi
        
        echo "开始构建容错增强版 (仅到CUDA阶段)..."
        if docker build -f Dockerfile.robust --target cuda-dev -t phoenix-v3-cuda-test:latest . --progress=plain; then
            echo ""
            echo -e "${GREEN}✅ CUDA阶段构建成功${NC}"
            
            # 运行CUDA验证
            echo -e "${YELLOW}🧪 运行CUDA验证...${NC}"
            docker run --rm --gpus all phoenix-v3-cuda-test:latest /bin/bash -c "
                echo '🔍 CUDA环境验证'
                echo '================'
                nvcc --version
                echo ''
                echo 'cuDNN运行时:'
                dpkg -l | grep libcudnn8
                echo ''
                echo 'cuDNN头文件:'
                ls -la /usr/include/cudnn* 2>/dev/null || echo '未找到'
                echo ''
                echo 'cuDNN库文件:'
                find /usr/lib -name 'libcudnn*' 2>/dev/null | head -3
                echo ''
                echo '✅ CUDA验证完成'
            "
            
        else
            echo ""
            echo -e "${RED}❌ 容错增强版构建失败${NC}"
        fi
        ;;
        
    4)
        echo -e "${YELLOW}⚡ 快速CUDA环境验证...${NC}"
        echo ""
        
        # 检查NVIDIA驱动
        if command -v nvidia-smi &> /dev/null; then
            echo -e "${GREEN}✅${NC} NVIDIA驱动可用"
            nvidia-smi --query-gpu=name,driver_version,memory.total --format=csv,noheader
        else
            echo -e "${RED}❌${NC} NVIDIA驱动不可用"
        fi
        
        echo ""
        
        # 检查Docker GPU支持
        if docker run --rm --gpus all nvidia/cuda:12.1.1-base-ubuntu22.04 nvidia-smi >/dev/null 2>&1; then
            echo -e "${GREEN}✅${NC} Docker GPU支持正常"
        else
            echo -e "${RED}❌${NC} Docker GPU支持异常"
        fi
        
        echo ""
        
        # 检查CUDA版本兼容性
        echo "检查CUDA版本兼容性..."
        docker run --rm nvidia/cuda:12.1.1-cudnn8-devel-ubuntu22.04 /bin/bash -c "
            echo 'CUDA版本:'
            nvcc --version | grep release
            echo ''
            echo 'cuDNN版本:'
            dpkg -l | grep libcudnn8 | head -1
            echo ''
            echo '可用的cuDNN开发包:'
            apt-get update >/dev/null 2>&1
            apt-cache madison libcudnn8-dev | head -3
        "
        ;;
        
    5)
        echo -e "${YELLOW}📊 生成修复报告...${NC}"
        echo ""
        
        # 创建报告文件
        REPORT_FILE="cudnn-fix-report-$(date +%Y%m%d-%H%M%S).md"
        
        cat > "$REPORT_FILE" << EOF
# CUDA/cuDNN依赖冲突修复报告

## 生成时间
$(date)

## 主机环境信息
EOF
        
        # 添加主机信息
        if command -v nvidia-smi &> /dev/null; then
            echo "" >> "$REPORT_FILE"
            echo "### GPU信息" >> "$REPORT_FILE"
            echo '```' >> "$REPORT_FILE"
            nvidia-smi --query-gpu=name,driver_version,memory.total --format=csv >> "$REPORT_FILE"
            echo '```' >> "$REPORT_FILE"
        fi
        
        # 添加Docker信息
        echo "" >> "$REPORT_FILE"
        echo "### Docker信息" >> "$REPORT_FILE"
        echo '```' >> "$REPORT_FILE"
        docker --version >> "$REPORT_FILE"
        echo '```' >> "$REPORT_FILE"
        
        # 添加CUDA基础镜像信息
        echo "" >> "$REPORT_FILE"
        echo "### CUDA基础镜像信息" >> "$REPORT_FILE"
        echo '```' >> "$REPORT_FILE"
        docker run --rm nvidia/cuda:12.1.1-cudnn8-devel-ubuntu22.04 /bin/bash -c "
            echo 'CUDA版本:'
            nvcc --version | grep release
            echo ''
            echo 'cuDNN运行时版本:'
            dpkg -l | grep libcudnn8 | head -1
        " >> "$REPORT_FILE" 2>/dev/null || echo "无法获取CUDA镜像信息" >> "$REPORT_FILE"
        echo '```' >> "$REPORT_FILE"
        
        # 添加解决方案
        cat >> "$REPORT_FILE" << 'EOF'

## 问题分析

### 错误原因
- cuDNN开发包版本与运行时版本不匹配
- NVIDIA CUDA镜像中的cuDNN版本与Ubuntu仓库中的开发包版本不同步
- 依赖链冲突导致包管理器无法解决版本冲突

### 解决方案

#### 方案1：智能版本匹配 (Dockerfile.robust)
- 动态检测当前cuDNN版本
- 尝试安装匹配版本的开发包
- 失败时创建兼容性链接
- 支持多重备选方案

#### 方案2：无冲突安装 (Dockerfile.no-conflict)  
- 避免通过apt安装cuDNN开发包
- 使用pip安装TensorRT
- 手动创建开发环境
- 确保最大兼容性

#### 方案3：修复脚本 (fix-cudnn-conflict.sh)
- 交互式修复工具
- 支持多种修复策略
- 自动环境验证
- 适合现有环境修复

## 使用建议

1. **新环境**: 使用 Dockerfile.no-conflict
2. **现有环境**: 使用 fix-cudnn-conflict.sh
3. **生产环境**: 使用 Dockerfile.robust (容错性更好)

## 验证方法

```bash
# 验证CUDA编译器
nvcc --version

# 验证cuDNN头文件
ls -la /usr/include/cudnn*

# 验证cuDNN库文件  
find /usr/lib -name "libcudnn*"

# 验证PyTorch CUDA支持
python -c "import torch; print(torch.cuda.is_available())"
```
EOF
        
        echo -e "${GREEN}✅ 修复报告已生成: $REPORT_FILE${NC}"
        echo ""
        echo "报告内容预览:"
        head -20 "$REPORT_FILE"
        echo "..."
        ;;
        
    *)
        echo -e "${RED}❌ 无效选择${NC}"
        exit 1
        ;;
esac

echo ""
echo -e "${BLUE}📋 修复方案总结：${NC}"
echo ""
echo -e "${GREEN}✅ 方案1${NC}: 智能版本匹配 (Dockerfile.robust)"
echo "   - 动态检测cuDNN版本"
echo "   - 多重备选安装策略"
echo "   - 适合复杂环境"
echo ""
echo -e "${GREEN}✅ 方案2${NC}: 无冲突安装 (Dockerfile.no-conflict)"
echo "   - 避免apt包冲突"
echo "   - 使用pip和手动安装"
echo "   - 适合新环境"
echo ""
echo -e "${GREEN}✅ 方案3${NC}: 交互式修复 (fix-cudnn-conflict.sh)"
echo "   - 现有环境修复"
echo "   - 多种修复策略"
echo "   - 自动验证"
echo ""
echo -e "${YELLOW}💡 建议${NC}: 根据具体情况选择合适的方案"

echo ""
echo -e "${GREEN}🎉 CUDA/cuDNN修复测试完成！${NC}"
