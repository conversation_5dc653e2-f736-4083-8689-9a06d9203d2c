#!/bin/bash
# =============================================================================
# PyTorch版本冲突解决方案测试脚本
# 用于验证不同Dockerfile方案的构建和功能
# =============================================================================

set -euo pipefail

# 颜色输出函数
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $*"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $*"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $*"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $*"
}

log_header() {
    echo -e "${PURPLE}========================================${NC}"
    echo -e "${WHITE}$*${NC}"
    echo -e "${PURPLE}========================================${NC}"
}

# 配置变量
WORKSPACE_DIR="/workspace/tools/bilibili-quiz-slover"
LOG_DIR="${WORKSPACE_DIR}/test-logs"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# 创建日志目录
mkdir -p "$LOG_DIR"

# Dockerfile方案列表
declare -A DOCKERFILES=(
    ["pytorch-upgrade"]="Dockerfile.pytorch-upgrade"
    ["progressive-install"]="Dockerfile.progressive-install" 
    ["cuda-downgrade"]="Dockerfile.cuda-downgrade"
)

# 镜像标签前缀
IMAGE_PREFIX="pytorch-solution"

# 测试函数
test_docker_build() {
    local solution_name=$1
    local dockerfile=$2
    local image_tag="${IMAGE_PREFIX}:${solution_name}"
    local log_file="${LOG_DIR}/build_${solution_name}_${TIMESTAMP}.log"
    
    log_header "测试方案: $solution_name"
    log_info "Dockerfile: $dockerfile"
    log_info "镜像标签: $image_tag"
    log_info "日志文件: $log_file"
    
    # 检查Dockerfile是否存在
    if [[ ! -f "$dockerfile" ]]; then
        log_error "Dockerfile不存在: $dockerfile"
        return 1
    fi
    
    # 构建镜像
    log_info "开始构建镜像..."
    local build_start_time=$(date +%s)
    
    if docker build -f "$dockerfile" -t "$image_tag" . 2>&1 | tee "$log_file"; then
        local build_end_time=$(date +%s)
        local build_duration=$((build_end_time - build_start_time))
        log_success "镜像构建成功！耗时: ${build_duration}秒"
        
        # 验证镜像
        test_image_functionality "$image_tag" "$solution_name"
        return $?
    else
        log_error "镜像构建失败！检查日志: $log_file"
        return 1
    fi
}

test_image_functionality() {
    local image_tag=$1
    local solution_name=$2
    local test_log="${LOG_DIR}/test_${solution_name}_${TIMESTAMP}.log"
    
    log_info "测试镜像功能: $image_tag"
    
    # 基础功能测试
    log_info "1. 基础Python环境测试..."
    if docker run --rm "$image_tag" bash -c "source /opt/miniconda/bin/activate llm_dev && python --version" 2>&1 | tee -a "$test_log"; then
        log_success "Python环境: ✅"
    else
        log_error "Python环境: ❌"
        return 1
    fi
    
    # PyTorch导入测试
    log_info "2. PyTorch导入测试..."
    if docker run --rm "$image_tag" bash -c "source /opt/miniconda/bin/activate llm_dev && python -c 'import torch; print(f\"PyTorch版本: {torch.__version__}\")'" 2>&1 | tee -a "$test_log"; then
        log_success "PyTorch导入: ✅"
    else
        log_error "PyTorch导入: ❌"
        return 1
    fi
    
    # CUDA可用性测试（如果有GPU）
    log_info "3. CUDA可用性测试..."
    if docker run --gpus all --rm "$image_tag" bash -c "source /opt/miniconda/bin/activate llm_dev && python -c 'import torch; print(f\"CUDA可用: {torch.cuda.is_available()}\")'" 2>&1 | tee -a "$test_log"; then
        log_success "CUDA测试: ✅"
    else
        log_warning "CUDA测试: ⚠️ (可能无GPU环境)"
    fi
    
    # Transformers生态测试
    log_info "4. Transformers生态测试..."
    if docker run --rm "$image_tag" bash -c "source /opt/miniconda/bin/activate llm_dev && python -c 'import transformers; print(f\"Transformers版本: {transformers.__version__}\")'" 2>&1 | tee -a "$test_log"; then
        log_success "Transformers: ✅"
    else
        log_warning "Transformers: ⚠️"
    fi
    
    # 综合功能测试
    log_info "5. 综合功能测试..."
    if docker run --rm "$image_tag" bash -c "
        source /opt/miniconda/bin/activate llm_dev && 
        python -c '
import torch
import numpy as np
print(\"=== 综合功能测试 ===\")
print(f\"PyTorch版本: {torch.__version__}\")
print(f\"CUDA可用: {torch.cuda.is_available()}\")
if torch.cuda.is_available():
    print(f\"GPU数量: {torch.cuda.device_count()}\")
    print(f\"当前GPU: {torch.cuda.get_device_name(0) if torch.cuda.device_count() > 0 else \"N/A\"}\")
# 简单张量操作测试
x = torch.randn(100, 100)
y = torch.randn(100, 100)
z = torch.mm(x, y)
print(f\"张量运算测试: ✅ (结果形状: {z.shape})\")
print(\"=== 测试完成 ===\")
'" 2>&1 | tee -a "$test_log"; then
        log_success "综合功能测试: ✅"
        return 0
    else
        log_error "综合功能测试: ❌"
        return 1
    fi
}

cleanup_images() {
    log_info "清理测试镜像..."
    for solution_name in "${!DOCKERFILES[@]}"; do
        local image_tag="${IMAGE_PREFIX}:${solution_name}"
        if docker images "$image_tag" --format "table {{.Repository}}:{{.Tag}}" | grep -q "$image_tag"; then
            log_info "删除镜像: $image_tag"
            docker rmi "$image_tag" || log_warning "删除镜像失败: $image_tag"
        fi
    done
}

generate_test_report() {
    local report_file="${LOG_DIR}/test_report_${TIMESTAMP}.md"
    
    log_info "生成测试报告: $report_file"
    
    cat > "$report_file" << EOF
# PyTorch版本冲突解决方案测试报告

**测试时间**: $(date)
**测试环境**: $(uname -a)
**Docker版本**: $(docker --version)

## 测试结果摘要

| 方案 | 构建状态 | 功能测试 | 推荐度 |
|------|----------|----------|--------|
EOF

    for solution_name in "${!DOCKERFILES[@]}"; do
        local dockerfile="${DOCKERFILES[$solution_name]}"
        local build_log="${LOG_DIR}/build_${solution_name}_${TIMESTAMP}.log"
        local test_log="${LOG_DIR}/test_${solution_name}_${TIMESTAMP}.log"
        
        if [[ -f "$build_log" ]]; then
            if grep -q "Successfully built" "$build_log"; then
                local build_status="✅ 成功"
            else
                local build_status="❌ 失败"
            fi
        else
            local build_status="⚠️ 未测试"
        fi
        
        if [[ -f "$test_log" ]]; then
            if grep -q "综合功能测试: ✅" "$test_log"; then
                local test_status="✅ 通过"
            else
                local test_status="❌ 失败"
            fi
        else
            local test_status="⚠️ 未测试"
        fi
        
        # 推荐度评估
        local recommendation=""
        case "$solution_name" in
            "pytorch-upgrade")
                recommendation="⭐⭐⭐⭐⭐"
                ;;
            "progressive-install")
                recommendation="⭐⭐⭐⭐⭐"
                ;;
            "cuda-downgrade")
                recommendation="⭐⭐⭐"
                ;;
            *)
                recommendation="⭐⭐⭐"
                ;;
        esac
        
        echo "| $solution_name | $build_status | $test_status | $recommendation |" >> "$report_file"
    done
    
    cat >> "$report_file" << EOF

## 详细日志文件

EOF

    for solution_name in "${!DOCKERFILES[@]}"; do
        echo "### $solution_name" >> "$report_file"
        echo "- 构建日志: \`build_${solution_name}_${TIMESTAMP}.log\`" >> "$report_file"
        echo "- 测试日志: \`test_${solution_name}_${TIMESTAMP}.log\`" >> "$report_file"
        echo "" >> "$report_file"
    done
    
    cat >> "$report_file" << EOF
## 使用建议

1. **推荐方案**: pytorch-upgrade 或 progressive-install
2. **兼容性**: 如需PyTorch 2.1.2，使用cuda-downgrade方案
3. **生产环境**: 建议使用pytorch-upgrade方案
4. **开发环境**: 可使用progressive-install方案

## 故障排除

如果构建失败，请检查：
1. Docker环境是否正常
2. 网络连接是否畅通
3. 是否有足够的磁盘空间
4. 镜像源是否可访问

EOF

    log_success "测试报告生成完成: $report_file"
}

show_usage() {
    echo "使用方法: $0 [OPTIONS] [SOLUTION]"
    echo ""
    echo "选项:"
    echo "  -h, --help              显示帮助信息"
    echo "  -c, --cleanup           清理测试镜像"
    echo "  -a, --all               测试所有解决方案"
    echo "  -r, --report            仅生成报告"
    echo ""
    echo "可用方案:"
    for solution_name in "${!DOCKERFILES[@]}"; do
        echo "  $solution_name"
    done
    echo ""
    echo "示例:"
    echo "  $0 pytorch-upgrade      # 测试PyTorch升级方案"
    echo "  $0 --all               # 测试所有方案"
    echo "  $0 --cleanup           # 清理测试镜像"
}

# 主函数
main() {
    log_header "PyTorch版本冲突解决方案测试"
    
    # 检查Docker是否可用
    if ! command -v docker >/dev/null 2>&1; then
        log_error "Docker未安装或不可用"
        exit 1
    fi
    
    # 检查工作目录
    if [[ ! -d "$WORKSPACE_DIR" ]]; then
        log_error "工作目录不存在: $WORKSPACE_DIR"
        exit 1
    fi
    
    cd "$WORKSPACE_DIR"
    
    # 解析命令行参数
    case "${1:-}" in
        -h|--help)
            show_usage
            exit 0
            ;;
        -c|--cleanup)
            cleanup_images
            exit 0
            ;;
        -a|--all)
            log_info "测试所有解决方案..."
            local success_count=0
            local total_count=${#DOCKERFILES[@]}
            
            for solution_name in "${!DOCKERFILES[@]}"; do
                local dockerfile="${DOCKERFILES[$solution_name]}"
                if test_docker_build "$solution_name" "$dockerfile"; then
                    ((success_count++))
                fi
                echo ""
            done
            
            generate_test_report
            
            log_header "测试总结"
            log_info "成功: $success_count/$total_count"
            
            if [[ $success_count -eq $total_count ]]; then
                log_success "所有方案测试通过！"
                exit 0
            else
                log_warning "部分方案测试失败"
                exit 1
            fi
            ;;
        -r|--report)
            generate_test_report
            exit 0
            ;;
        "")
            log_error "请指定要测试的方案或使用 --all"
            show_usage
            exit 1
            ;;
        *)
            local solution_name="$1"
            if [[ -n "${DOCKERFILES[$solution_name]:-}" ]]; then
                local dockerfile="${DOCKERFILES[$solution_name]}"
                if test_docker_build "$solution_name" "$dockerfile"; then
                    log_success "方案 $solution_name 测试通过！"
                    exit 0
                else
                    log_error "方案 $solution_name 测试失败！"
                    exit 1
                fi
            else
                log_error "未知方案: $solution_name"
                show_usage
                exit 1
            fi
            ;;
    esac
}

# 信号处理
trap cleanup_images EXIT

# 执行主函数
main "$@"