#!/bin/bash

# 容错增强版Dockerfile构建测试脚本
# 支持分阶段构建和错误恢复

set -e

# 颜色定义
BLUE='\033[36m'
GREEN='\033[32m'
YELLOW='\033[33m'
RED='\033[31m'
NC='\033[0m'

# 配置
DOCKERFILE="Dockerfile.robust"
IMAGE_NAME="multi-lang-robust"
BUILD_LOG="build.log"

echo -e "${BLUE}🔥 凤凰涅槃计划V3 - 容错增强版构建测试${NC}"
echo "=================================================="
echo ""

# 检查Docker环境
if ! command -v docker &> /dev/null; then
    echo -e "${RED}❌ Docker未安装，请先安装Docker${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Docker环境检查通过${NC}"
echo ""

# 检查Dockerfile
if [ ! -f "$DOCKERFILE" ]; then
    echo -e "${RED}❌ 找不到 $DOCKERFILE${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Dockerfile检查通过${NC}"
echo ""

# 显示构建选项
echo -e "${YELLOW}📋 构建选项：${NC}"
echo "1. 完整构建 (所有阶段)"
echo "2. 分阶段构建测试"
echo "3. 快速验证构建"
echo "4. 查看构建阶段"
echo ""

read -p "请选择构建选项 (1-4): " choice

case $choice in
    1)
        echo -e "${YELLOW}🔨 开始完整构建...${NC}"
        echo "构建日志将保存到: $BUILD_LOG"
        echo ""
        
        if docker build -f "$DOCKERFILE" -t "$IMAGE_NAME:latest" . --progress=plain 2>&1 | tee "$BUILD_LOG"; then
            echo ""
            echo -e "${GREEN}🎉 完整构建成功！${NC}"
            
            # 运行验证测试
            echo -e "${YELLOW}🧪 运行环境验证...${NC}"
            docker run --rm --gpus all "$IMAGE_NAME:latest" /bin/bash -c "
                echo '🔍 环境验证测试'
                echo '=================='
                nvidia-smi --query-gpu=name --format=csv,noheader | head -1
                nvcc --version | grep release
                go version
                source /root/.cargo/env && rustc --version
                source /opt/miniconda/bin/activate llm_dev && python -c 'import torch; print(f\"PyTorch: {torch.__version__}, CUDA: {torch.cuda.is_available()}\")'
                echo '✅ 所有环境验证通过'
            "
            
        else
            echo ""
            echo -e "${RED}❌ 完整构建失败${NC}"
            echo "请查看构建日志: $BUILD_LOG"
        fi
        ;;
        
    2)
        echo -e "${YELLOW}🔨 分阶段构建测试...${NC}"
        echo ""
        
        # 定义构建阶段
        stages=("base-system" "cuda-dev" "cpp-dev" "go-dev" "rust-dev" "python-dev" "final")
        
        for stage in "${stages[@]}"; do
            echo -e "${BLUE}📦 构建阶段: $stage${NC}"
            
            if docker build -f "$DOCKERFILE" --target "$stage" -t "$IMAGE_NAME:$stage" . --progress=plain; then
                echo -e "${GREEN}✅ 阶段 $stage 构建成功${NC}"
                
                # 简单验证
                echo -e "${YELLOW}🔍 验证阶段 $stage...${NC}"
                docker run --rm "$IMAGE_NAME:$stage" /bin/bash -c "
                    echo '阶段验证: $stage'
                    case '$stage' in
                        'base-system')
                            which retry_cmd && echo 'retry_cmd: ✅' || echo 'retry_cmd: ❌'
                            which optional_install && echo 'optional_install: ✅' || echo 'optional_install: ❌'
                            ;;
                        'cuda-dev')
                            nvcc --version && echo 'CUDA: ✅' || echo 'CUDA: ❌'
                            ;;
                        'cpp-dev')
                            gcc --version && echo 'GCC: ✅' || echo 'GCC: ❌'
                            ;;
                        'go-dev')
                            go version && echo 'Go: ✅' || echo 'Go: ❌'
                            ;;
                        'rust-dev')
                            source /root/.cargo/env && rustc --version && echo 'Rust: ✅' || echo 'Rust: ❌'
                            ;;
                        'python-dev')
                            source /opt/miniconda/bin/activate llm_dev && python --version && echo 'Python: ✅' || echo 'Python: ❌'
                            ;;
                        'final')
                            echo '最终验证完成'
                            ;;
                    esac
                "
                echo ""
            else
                echo -e "${RED}❌ 阶段 $stage 构建失败${NC}"
                echo "可以从此阶段重新开始构建"
                break
            fi
        done
        ;;
        
    3)
        echo -e "${YELLOW}🔨 快速验证构建...${NC}"
        echo "只构建到CUDA开发阶段进行快速验证"
        echo ""
        
        if docker build -f "$DOCKERFILE" --target "cuda-dev" -t "$IMAGE_NAME:quick-test" . --progress=plain; then
            echo ""
            echo -e "${GREEN}✅ 快速验证构建成功${NC}"
            
            # 运行CUDA验证
            echo -e "${YELLOW}🧪 CUDA环境验证...${NC}"
            docker run --rm --gpus all "$IMAGE_NAME:quick-test" /bin/bash -c "
                echo '🔍 CUDA快速验证'
                echo '================'
                nvidia-smi --query-gpu=name --format=csv,noheader | head -1
                nvcc --version
                find /usr/local/cuda -name '*.so' | head -3
                echo '✅ CUDA验证通过'
            "
            
        else
            echo ""
            echo -e "${RED}❌ 快速验证构建失败${NC}"
        fi
        ;;
        
    4)
        echo -e "${YELLOW}📋 构建阶段信息：${NC}"
        echo ""
        echo "1. base-system  - 基础系统环境 (镜像源、重试机制、基础工具)"
        echo "2. cuda-dev     - CUDA开发环境 (NVCC、cuDNN、数学库)"
        echo "3. cpp-dev      - C++高性能环境 (GCC、Clang、性能工具)"
        echo "4. go-dev       - Go微服务环境 (Go编译器、开发工具)"
        echo "5. rust-dev     - Rust系统环境 (Rust工具链、组件)"
        echo "6. python-dev   - Python AI/ML环境 (PyTorch、Transformers)"
        echo "7. final        - 最终配置 (Shell、项目结构、启动脚本)"
        echo ""
        echo "使用方法："
        echo "docker build -f $DOCKERFILE --target <阶段名> -t <镜像名> ."
        ;;
        
    *)
        echo -e "${RED}❌ 无效选择${NC}"
        exit 1
        ;;
esac

echo ""
echo -e "${BLUE}📋 容错机制特性：${NC}"
echo "✅ 多重镜像源备选 (阿里云、清华、中科大、官方)"
echo "✅ 指数退避重试机制"
echo "✅ 可选组件安装 (失败不影响主要功能)"
echo "✅ 智能依赖冲突处理"
echo "✅ 分阶段构建支持"
echo "✅ 详细构建日志"
echo "✅ 环境验证检查"

echo ""
echo -e "${GREEN}🎯 构建测试完成！${NC}"

if [ "$choice" = "1" ] && [ -f "$BUILD_LOG" ]; then
    echo ""
    echo -e "${YELLOW}📊 构建统计：${NC}"
    echo "总构建步骤: $(grep -c "Step " "$BUILD_LOG" || echo "未知")"
    echo "成功步骤: $(grep -c "Successfully built\|✅" "$BUILD_LOG" || echo "未知")"
    echo "警告信息: $(grep -c "⚠️\|WARNING" "$BUILD_LOG" || echo "0")"
    echo "构建日志: $BUILD_LOG"
fi
