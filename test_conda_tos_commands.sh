#!/bin/bash
# Conda服务条款命令测试脚本

echo "🧪 测试Conda服务条款相关命令..."

# 模拟conda命令 (仅语法检查)
echo "检查conda tos accept命令语法..."

# 检查主频道
echo "conda tos accept --override-channels --channel https://repo.anaconda.com/pkgs/main"

# 检查R频道  
echo "conda tos accept --override-channels --channel https://repo.anaconda.com/pkgs/r"

# 检查free频道
echo "conda tos accept --override-channels --channel https://repo.anaconda.com/pkgs/free"

# 检查msys2频道
echo "conda tos accept --override-channels --channel https://repo.anaconda.com/pkgs/msys2"

# 检查全局设置
echo "conda config --set tos_accepted true"

# 检查环境创建
echo "conda create -n ai python=3.10 -y --override-channels --channel conda-forge --channel defaults"

echo "✅ 所有命令语法检查完成"
