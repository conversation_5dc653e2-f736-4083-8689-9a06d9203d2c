#!/bin/bash
# 测试Miniconda修复效果

set -e

echo "🧪 测试Miniconda修复效果..."

# 测试安装脚本语法
echo "1. 测试安装脚本语法..."
if bash -n install_miniconda_robust.sh; then
    echo "✅ 安装脚本语法正确"
else
    echo "❌ 安装脚本语法错误"
    exit 1
fi

# 测试Dockerfile语法
echo "2. 测试Dockerfile语法..."
if command -v docker >/dev/null 2>&1; then
    if timeout 60 docker build -f Dockerfile.robust -t test-miniconda-fix . --target base-system >/dev/null 2>&1; then
        echo "✅ Dockerfile语法正确"
        docker rmi test-miniconda-fix >/dev/null 2>&1 || true
    else
        echo "❌ Dockerfile语法错误"
        exit 1
    fi
else
    echo "⚠️ Docker不可用，跳过语法检查"
fi

# 检查关键文件
echo "3. 检查关键文件..."
files=("install_miniconda_robust.sh" "verify_downloads.sh")
for file in "${files[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ 找到: $file"
    else
        echo "❌ 缺失: $file"
        exit 1
    fi
done

echo "🎉 所有测试通过！"
