#!/bin/bash
# PostgreSQL修复测试脚本
# 用于验证PostgreSQL安装问题是否已解决

set -e

echo "🧪 测试PostgreSQL安装修复..."

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${YELLOW}ℹ️ $1${NC}"
}

# 测试1：检查安装脚本是否存在
test_script_exists() {
    print_info "测试1: 检查PostgreSQL安装脚本..."
    if [ -f "install_postgresql.sh" ]; then
        print_success "PostgreSQL安装脚本存在"
        return 0
    else
        print_error "PostgreSQL安装脚本不存在"
        return 1
    fi
}

# 测试2：检查脚本语法
test_script_syntax() {
    print_info "测试2: 检查脚本语法..."
    if bash -n install_postgresql.sh; then
        print_success "脚本语法正确"
        return 0
    else
        print_error "脚本语法错误"
        return 1
    fi
}

# 测试3：检查Dockerfile修改
test_dockerfile_changes() {
    print_info "测试3: 检查Dockerfile修改..."
    
    # 检查是否包含COPY指令
    if grep -q "COPY install_postgresql.sh" Dockerfile.robust; then
        print_success "Dockerfile包含PostgreSQL脚本复制指令"
    else
        print_error "Dockerfile缺少PostgreSQL脚本复制指令"
        return 1
    fi
    
    # 检查是否移除了有问题的包名
    if ! grep -q "postgresql-client-\${POSTGRESQL_VERSION}" Dockerfile.robust; then
        print_success "已移除有问题的PostgreSQL客户端包名"
    else
        print_error "仍然包含有问题的PostgreSQL客户端包名"
        return 1
    fi
    
    return 0
}

# 测试4：模拟构建测试（仅检查语法）
test_build_syntax() {
    print_info "测试4: 检查Docker构建语法..."
    
    # 创建临时Dockerfile进行语法检查
    cat > Dockerfile.test << 'EOF'
FROM ubuntu:22.04
COPY install_postgresql.sh /tmp/install_postgresql.sh
RUN chmod +x /tmp/install_postgresql.sh
ENV POSTGRESQL_VERSION=15
RUN echo "模拟测试：/tmp/install_postgresql.sh ${POSTGRESQL_VERSION}"
EOF
    
    if docker build -f Dockerfile.test -t test-postgresql . >/dev/null 2>&1; then
        print_success "Docker构建语法测试通过"
        docker rmi test-postgresql >/dev/null 2>&1 || true
        rm -f Dockerfile.test
        return 0
    else
        print_error "Docker构建语法测试失败"
        rm -f Dockerfile.test
        return 1
    fi
}

# 测试5：检查包可用性（在Ubuntu 22.04容器中）
test_package_availability() {
    print_info "测试5: 检查PostgreSQL包可用性..."
    
    # 创建测试脚本
    cat > test_packages.sh << 'EOF'
#!/bin/bash
apt-get update >/dev/null 2>&1
echo "检查默认源中的PostgreSQL包:"
apt-cache show postgresql-15 >/dev/null 2>&1 && echo "✅ postgresql-15 可用" || echo "❌ postgresql-15 不可用"
apt-cache show postgresql-client-15 >/dev/null 2>&1 && echo "✅ postgresql-client-15 可用" || echo "❌ postgresql-client-15 不可用"
apt-cache show postgresql >/dev/null 2>&1 && echo "✅ postgresql (通用) 可用" || echo "❌ postgresql (通用) 不可用"
apt-cache show postgresql-client >/dev/null 2>&1 && echo "✅ postgresql-client (通用) 可用" || echo "❌ postgresql-client (通用) 不可用"
EOF
    
    chmod +x test_packages.sh
    
    if docker run --rm -v "$(pwd)/test_packages.sh:/test_packages.sh" ubuntu:22.04 /test_packages.sh; then
        print_success "包可用性测试完成"
        rm -f test_packages.sh
        return 0
    else
        print_error "包可用性测试失败"
        rm -f test_packages.sh
        return 1
    fi
}

# 主测试函数
main() {
    echo "🚀 开始PostgreSQL修复验证测试..."
    echo "=================================================="
    
    local tests_passed=0
    local total_tests=5
    
    # 运行所有测试
    test_script_exists && ((tests_passed++))
    test_script_syntax && ((tests_passed++))
    test_dockerfile_changes && ((tests_passed++))
    test_build_syntax && ((tests_passed++))
    test_package_availability && ((tests_passed++))
    
    echo ""
    echo "=================================================="
    echo "📊 测试结果总结:"
    echo "通过测试: $tests_passed/$total_tests"
    
    if [ $tests_passed -eq $total_tests ]; then
        print_success "🎉 所有测试通过！PostgreSQL安装问题已修复"
        echo ""
        echo "📋 修复内容总结:"
        echo "1. ✅ 创建了智能PostgreSQL安装脚本"
        echo "2. ✅ 支持多种安装源（默认源 + 官方源 + 通用版本）"
        echo "3. ✅ 修复了Dockerfile中的包名问题"
        echo "4. ✅ 添加了错误处理和重试机制"
        echo "5. ✅ 支持PostGIS扩展的可选安装"
        echo ""
        echo "🚀 现在可以重新运行Docker构建："
        echo "   docker-compose -f docker-compose.v4.yml build"
        return 0
    else
        print_error "❌ 部分测试失败，需要进一步检查"
        return 1
    fi
}

# 执行测试
main "$@"
