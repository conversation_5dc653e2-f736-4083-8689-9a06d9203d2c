#!/bin/bash

# 凤凰涅槃计划V3配置验证脚本
# 验证多语言开发环境配置的完整性

set -e

# 颜色定义
BLUE='\033[36m'
GREEN='\033[32m'
YELLOW='\033[33m'
RED='\033[31m'
NC='\033[0m'

echo -e "${BLUE}🔥 凤凰涅槃计划V3：配置验证${NC}"
echo "=============================================="
echo ""

# 验证Dockerfile
echo -e "${YELLOW}📋 验证Dockerfile配置...${NC}"
if [ -f "Dockerfile" ]; then
    echo -e "${GREEN}✓${NC} Dockerfile存在"
    
    # 检查关键配置
    if grep -q "凤凰涅槃计划V3" Dockerfile; then
        echo -e "${GREEN}✓${NC} 项目标题正确"
    else
        echo -e "${RED}✗${NC} 项目标题需要更新"
    fi
    
    if grep -q "CUDA GPU计算、Go微服务、C++高性能、Python AI/ML" Dockerfile; then
        echo -e "${GREEN}✓${NC} 多语言支持描述正确"
    else
        echo -e "${RED}✗${NC} 多语言支持描述需要更新"
    fi
    
    if grep -q "GOPATH=/workspace/go" Dockerfile; then
        echo -e "${GREEN}✓${NC} Go环境变量配置正确"
    else
        echo -e "${RED}✗${NC} Go环境变量配置缺失"
    fi
    
    if grep -q "RUSTUP_HOME=/root/.rustup" Dockerfile; then
        echo -e "${GREEN}✓${NC} Rust环境变量配置正确"
    else
        echo -e "${RED}✗${NC} Rust环境变量配置缺失"
    fi
    
    if grep -q "go install golang.org/x/tools/gopls@latest" Dockerfile; then
        echo -e "${GREEN}✓${NC} Go工具安装配置正确"
    else
        echo -e "${RED}✗${NC} Go工具安装配置缺失"
    fi
    
    if grep -q "rustup component add rust-analyzer" Dockerfile; then
        echo -e "${GREEN}✓${NC} Rust工具安装配置正确"
    else
        echo -e "${RED}✗${NC} Rust工具安装配置缺失"
    fi
    
    if grep -q "tensorrt" Dockerfile; then
        echo -e "${GREEN}✓${NC} TensorRT支持已添加"
    else
        echo -e "${RED}✗${NC} TensorRT支持缺失"
    fi
    
    if grep -q "pybind11" Dockerfile; then
        echo -e "${GREEN}✓${NC} pybind11支持已添加"
    else
        echo -e "${RED}✗${NC} pybind11支持缺失"
    fi
    
else
    echo -e "${RED}✗${NC} Dockerfile不存在"
fi

echo ""

# 验证docker-compose.yml
echo -e "${YELLOW}🐳 验证docker-compose.yml配置...${NC}"
if [ -f "docker-compose.yml" ]; then
    echo -e "${GREEN}✓${NC} docker-compose.yml存在"
    
    if grep -q "phoenix-v3" docker-compose.yml; then
        echo -e "${GREEN}✓${NC} 服务名称已更新"
    else
        echo -e "${RED}✗${NC} 服务名称需要更新"
    fi
    
    if grep -q "GOPATH=/workspace/go" docker-compose.yml; then
        echo -e "${GREEN}✓${NC} Go环境变量配置正确"
    else
        echo -e "${RED}✗${NC} Go环境变量配置缺失"
    fi
    
    if grep -q "RUSTUP_HOME=/root/.rustup" docker-compose.yml; then
        echo -e "${GREEN}✓${NC} Rust环境变量配置正确"
    else
        echo -e "${RED}✗${NC} Rust环境变量配置缺失"
    fi
    
else
    echo -e "${RED}✗${NC} docker-compose.yml不存在"
fi

echo ""

# 验证README.md
echo -e "${YELLOW}📖 验证README.md配置...${NC}"
if [ -f "README.md" ]; then
    echo -e "${GREEN}✓${NC} README.md存在"
    
    if grep -q "凤凰涅槃计划V3" README.md; then
        echo -e "${GREEN}✓${NC} 项目标题已更新"
    else
        echo -e "${RED}✗${NC} 项目标题需要更新"
    fi
    
    if grep -q "libcuda_ops" README.md; then
        echo -e "${GREEN}✓${NC} 核心项目描述已添加"
    else
        echo -e "${RED}✗${NC} 核心项目描述缺失"
    fi
    
    if grep -q "Go微服务开发" README.md; then
        echo -e "${GREEN}✓${NC} Go微服务部分已添加"
    else
        echo -e "${RED}✗${NC} Go微服务部分缺失"
    fi
    
    if grep -q "C++高性能开发" README.md; then
        echo -e "${GREEN}✓${NC} C++高性能部分已添加"
    else
        echo -e "${RED}✗${NC} C++高性能部分缺失"
    fi
    
else
    echo -e "${RED}✗${NC} README.md不存在"
fi

echo ""

# 验证Makefile
echo -e "${YELLOW}🔧 验证Makefile配置...${NC}"
if [ -f "Makefile" ]; then
    echo -e "${GREEN}✓${NC} Makefile存在"
    
    if grep -q "phoenix-v3-ai-dev" Makefile; then
        echo -e "${GREEN}✓${NC} 项目名称已更新"
    else
        echo -e "${RED}✗${NC} 项目名称需要更新"
    fi
    
    if grep -q "phoenix-v3" Makefile; then
        echo -e "${GREEN}✓${NC} 服务名称已更新"
    else
        echo -e "${RED}✗${NC} 服务名称需要更新"
    fi
    
else
    echo -e "${RED}✗${NC} Makefile不存在"
fi

echo ""

# 验证setup-environment.sh
echo -e "${YELLOW}⚙️ 验证setup-environment.sh配置...${NC}"
if [ -f "setup-environment.sh" ]; then
    echo -e "${GREEN}✓${NC} setup-environment.sh存在"
    
    if grep -q "凤凰涅槃计划V3" setup-environment.sh; then
        echo -e "${GREEN}✓${NC} 脚本标题已更新"
    else
        echo -e "${RED}✗${NC} 脚本标题需要更新"
    fi
    
else
    echo -e "${RED}✗${NC} setup-environment.sh不存在"
fi

echo ""
echo -e "${BLUE}🎯 配置验证完成！${NC}"
echo "=============================================="

# 显示项目结构
echo ""
echo -e "${YELLOW}📁 当前项目结构:${NC}"
ls -la

echo ""
echo -e "${GREEN}✨ 凤凰涅槃计划V3配置验证完成！${NC}"
echo ""
echo -e "${YELLOW}📋 下一步操作建议:${NC}"
echo "1. 如果有Docker环境，可以运行: docker build -t phoenix-v3-ai-dev:latest ."
echo "2. 使用docker-compose启动: docker-compose up -d"
echo "3. 进入开发环境: docker-compose exec phoenix-v3 zsh"
echo ""
