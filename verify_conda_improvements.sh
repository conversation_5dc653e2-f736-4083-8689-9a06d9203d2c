#!/bin/bash
# 验证Conda配置改进效果

echo "🔍 验证Conda配置改进..."
echo "=================================================="

# 检查.condarc配置文件创建
echo "1. 检查.condarc配置文件..."
if grep -q "tos_accepted: true" Dockerfile.robust; then
    echo "✅ 包含tos_accepted配置"
else
    echo "❌ 缺少tos_accepted配置"
fi

if grep -q "auto_update_conda: false" Dockerfile.robust; then
    echo "✅ 包含auto_update_conda配置"
else
    echo "❌ 缺少auto_update_conda配置"
fi

if grep -q "channel_priority: flexible" Dockerfile.robust; then
    echo "✅ 包含channel_priority配置"
else
    echo "❌ 缺少channel_priority配置"
fi

# 检查频道配置
echo ""
echo "2. 检查频道配置..."
if grep -q "conda-forge" Dockerfile.robust; then
    echo "✅ 包含conda-forge频道"
else
    echo "❌ 缺少conda-forge频道"
fi

if grep -q "defaults" Dockerfile.robust; then
    echo "✅ 包含defaults频道"
else
    echo "❌ 缺少defaults频道"
fi

if grep -q "mirrors.aliyun.com" Dockerfile.robust; then
    echo "✅ 包含阿里云镜像频道"
else
    echo "❌ 缺少阿里云镜像频道"
fi

# 检查环境变量设置
echo ""
echo "3. 检查环境变量设置..."
if grep -q "CONDA_TOS_ACCEPTED=true" Dockerfile.robust; then
    echo "✅ 包含CONDA_TOS_ACCEPTED环境变量"
else
    echo "❌ 缺少CONDA_TOS_ACCEPTED环境变量"
fi

if grep -q "CONDA_ALWAYS_YES=true" Dockerfile.robust; then
    echo "✅ 包含CONDA_ALWAYS_YES环境变量"
else
    echo "❌ 缺少CONDA_ALWAYS_YES环境变量"
fi

# 检查AI环境创建
echo ""
echo "4. 检查AI环境创建..."
if grep -A10 "创建AI开发环境" Dockerfile.robust | grep -q "python=3.10"; then
    echo "✅ 指定Python 3.10版本"
else
    echo "❌ 未指定Python版本"
fi

if grep -A10 "创建AI开发环境" Dockerfile.robust | grep -q "conda info --envs"; then
    echo "✅ 包含环境验证"
else
    echo "❌ 缺少环境验证"
fi

# 检查配置简洁性
echo ""
echo "5. 检查配置简洁性..."
conda_lines=$(grep -c "conda" Dockerfile.robust)
echo "📊 Conda相关行数: $conda_lines"

if [ $conda_lines -lt 30 ]; then
    echo "✅ 配置简洁 (少于30行)"
else
    echo "⚠️ 配置较复杂 (超过30行)"
fi

echo ""
echo "=================================================="
echo "🎉 Conda配置改进验证完成！"
echo ""
echo "📋 改进内容总结:"
echo "1. ✅ 统一的.condarc配置文件"
echo "2. ✅ 完整的服务条款接受设置"
echo "3. ✅ 优化的频道配置 (conda-forge优先)"
echo "4. ✅ 环境变量设置 (CONDA_TOS_ACCEPTED, CONDA_ALWAYS_YES)"
echo "5. ✅ 简化的AI环境创建流程"
echo "6. ✅ 完整的验证机制"
echo ""
echo "🚀 现在可以构建Docker镜像："
echo "   docker-compose -f docker-compose.v4.yml build --no-cache"
