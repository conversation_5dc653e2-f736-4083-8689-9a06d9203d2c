#!/bin/bash
# 下载验证脚本

verify_url() {
    local url="$1"
    local name="$2"
    
    echo "验证 $name: $url"
    
    if timeout 10 curl -sSL --head "$url" >/dev/null 2>&1; then
        echo "✅ $name 可用"
        return 0
    else
        echo "❌ $name 不可用"
        return 1
    fi
}

echo "🔍 验证关键下载源..."

# 验证Miniconda源
verify_url "https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh" "Anaconda官方源"
verify_url "https://mirrors.tuna.tsinghua.edu.cn/anaconda/miniconda/Miniconda3-latest-Linux-x86_64.sh" "清华镜像"
verify_url "https://mirrors.aliyun.com/anaconda/miniconda/Miniconda3-latest-Linux-x86_64.sh" "阿里云镜像"

# 验证PyTorch源
verify_url "https://download.pytorch.org/whl/cu122/torch-2.2.0%2Bcu122-cp310-cp310-linux_x86_64.whl" "PyTorch官方源"

echo "验证完成"
