#!/bin/bash
# 简化的PostgreSQL修复验证脚本

echo "🔍 验证PostgreSQL安装修复..."

# 1. 检查安装脚本存在
if [ -f "install_postgresql.sh" ]; then
    echo "✅ PostgreSQL安装脚本存在"
else
    echo "❌ PostgreSQL安装脚本不存在"
    exit 1
fi

# 2. 检查脚本语法
if bash -n install_postgresql.sh; then
    echo "✅ PostgreSQL安装脚本语法正确"
else
    echo "❌ PostgreSQL安装脚本语法错误"
    exit 1
fi

# 3. 检查Dockerfile修改
if grep -q "COPY install_postgresql.sh" Dockerfile.robust; then
    echo "✅ Dockerfile包含PostgreSQL脚本复制指令"
else
    echo "❌ Dockerfile缺少PostgreSQL脚本复制指令"
    exit 1
fi

# 4. 检查是否移除了有问题的包名
if ! grep -q "postgresql-client-\${POSTGRESQL_VERSION}" Dockerfile.robust; then
    echo "✅ 已移除有问题的PostgreSQL客户端包名"
else
    echo "❌ 仍然包含有问题的PostgreSQL客户端包名"
    exit 1
fi

# 5. 检查通用PostgreSQL客户端包名
if grep -q "postgresql-client redis-tools" Dockerfile.robust; then
    echo "✅ 使用了通用PostgreSQL客户端包名"
else
    echo "❌ 未使用通用PostgreSQL客户端包名"
    exit 1
fi

echo ""
echo "🎉 PostgreSQL安装问题修复验证完成！"
echo ""
echo "📋 修复内容总结:"
echo "1. ✅ 创建了智能PostgreSQL安装脚本 (install_postgresql.sh)"
echo "2. ✅ 支持多种安装源（默认源 → 官方源 → 通用版本）"
echo "3. ✅ 修复了Dockerfile中的版本特定包名问题"
echo "4. ✅ 添加了错误处理和重试机制"
echo "5. ✅ 支持PostGIS扩展的可选安装"
echo ""
echo "🚀 现在可以重新运行Docker构建："
echo "   docker-compose -f docker-compose.v4.yml build"
echo "   或者："
echo "   docker compose -f docker-compose.v4.yml build"
