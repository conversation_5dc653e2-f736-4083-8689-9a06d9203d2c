#!/usr/bin/env python3
"""
凤凰涅槃计划V4环境验证脚本
验证所有V4新增的企业级组件和功能
"""

import sys
import subprocess
import importlib
from typing import Dict, List, Tuple

class V4EnvironmentVerifier:
    """V4环境验证器"""
    
    def __init__(self):
        self.results = {}
        self.total_checks = 0
        self.passed_checks = 0
    
    def check_import(self, module_name: str, description: str) -> bool:
        """检查Python模块导入"""
        try:
            importlib.import_module(module_name)
            self.log_success(f"{description}: ✅")
            return True
        except ImportError as e:
            self.log_error(f"{description}: ❌ ({e})")
            return False
    
    def check_command(self, command: List[str], description: str) -> bool:
        """检查命令行工具"""
        try:
            result = subprocess.run(command, capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                self.log_success(f"{description}: ✅")
                return True
            else:
                self.log_error(f"{description}: ❌ (exit code: {result.returncode})")
                return False
        except Exception as e:
            self.log_error(f"{description}: ❌ ({e})")
            return False
    
    def log_success(self, message: str):
        """记录成功"""
        print(message)
        self.passed_checks += 1
        self.total_checks += 1
    
    def log_error(self, message: str):
        """记录错误"""
        print(message)
        self.total_checks += 1
    
    def verify_core_environment(self):
        """验证核心环境"""
        print("🔍 验证核心环境...")
        
        # Python环境
        self.check_import("torch", "PyTorch")
        self.check_import("transformers", "Transformers")
        
        # V4新增：多模态AI框架
        self.check_import("jax", "JAX")
        self.check_import("flax", "Flax")
        self.check_import("optax", "Optax")
        
        # CUDA验证
        try:
            import torch
            if torch.cuda.is_available():
                self.log_success(f"CUDA可用: ✅ (设备数: {torch.cuda.device_count()})")
            else:
                self.log_error("CUDA可用: ❌")
        except:
            self.log_error("CUDA验证: ❌")
    
    def verify_database_environment(self):
        """验证数据库环境"""
        print("\n🗄️ 验证数据库环境...")
        
        # 数据库连接器
        self.check_import("psycopg2", "PostgreSQL连接器")
        self.check_import("redis", "Redis连接器")
        self.check_import("py2neo", "Neo4j连接器")
        self.check_import("clickhouse_driver", "ClickHouse连接器")
        
        # 数据库命令行工具
        self.check_command(["psql", "--version"], "PostgreSQL客户端")
        self.check_command(["redis-cli", "--version"], "Redis客户端")
    
    def verify_mlops_environment(self):
        """验证MLOps环境"""
        print("\n📊 验证MLOps环境...")
        
        # MLOps工具
        self.check_import("mlflow", "MLflow")
        self.check_import("dvc", "DVC")
        
        # 工作流工具
        try:
            import airflow
            self.log_success("Apache Airflow: ✅")
        except ImportError:
            self.log_error("Apache Airflow: ❌")
        
        # Kubernetes工具
        self.check_command(["kubectl", "version", "--client"], "Kubernetes客户端")
        self.check_command(["helm", "version"], "Helm")
    
    def verify_cloud_native_environment(self):
        """验证云原生环境"""
        print("\n☁️ 验证云原生环境...")
        
        # 监控工具
        self.check_import("prometheus_client", "Prometheus客户端")
        self.check_import("jaeger_client", "Jaeger客户端")
        self.check_import("opentracing", "OpenTracing")
        
        # 日志工具
        self.check_import("elasticsearch", "Elasticsearch客户端")
        self.check_import("loguru", "Loguru日志")
        self.check_import("structlog", "StructLog")
        
        # 消息队列
        self.check_import("kafka", "Kafka客户端")
    
    def verify_development_tools(self):
        """验证开发工具"""
        print("\n🛠️ 验证开发工具...")
        
        # 编程语言
        self.check_command(["go", "version"], "Go语言")
        self.check_command(["rustc", "--version"], "Rust语言")
        self.check_command(["gcc", "--version"], "GCC编译器")
        
        # 开发工具
        self.check_import("jupyter", "Jupyter")
        self.check_import("black", "Black代码格式化")
        self.check_import("pytest", "PyTest测试框架")
    
    def verify_ai_frameworks(self):
        """验证AI框架"""
        print("\n🧠 验证AI框架...")
        
        # 传统框架
        self.check_import("torch", "PyTorch")
        self.check_import("transformers", "Transformers")
        
        # V4新增框架
        self.check_import("jax", "JAX")
        self.check_import("sentence_transformers", "Sentence Transformers")
        
        # 推理优化
        try:
            import tensorrt
            self.log_success("TensorRT: ✅")
        except ImportError:
            self.log_error("TensorRT: ❌ (可能需要GPU环境)")
        
        # 向量搜索
        self.check_import("faiss", "Faiss")
    
    def verify_gpu_environment(self):
        """验证GPU环境"""
        print("\n🎮 验证GPU环境...")
        
        try:
            import torch
            if torch.cuda.is_available():
                device_count = torch.cuda.device_count()
                self.log_success(f"GPU设备数量: {device_count} ✅")
                
                for i in range(device_count):
                    gpu_name = torch.cuda.get_device_name(i)
                    gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
                    self.log_success(f"GPU {i}: {gpu_name} ({gpu_memory:.1f}GB) ✅")
                
                # 测试JAX GPU支持
                try:
                    import jax
                    jax_devices = jax.devices()
                    self.log_success(f"JAX GPU设备: {len(jax_devices)} ✅")
                except:
                    self.log_error("JAX GPU支持: ❌")
            else:
                self.log_error("CUDA不可用: ❌")
        except Exception as e:
            self.log_error(f"GPU环境检查失败: ❌ ({e})")
    
    def run_comprehensive_test(self):
        """运行综合测试"""
        print("🚀 凤凰涅槃计划V4环境综合验证")
        print("=" * 50)
        
        # 执行所有验证
        self.verify_core_environment()
        self.verify_database_environment()
        self.verify_mlops_environment()
        self.verify_cloud_native_environment()
        self.verify_development_tools()
        self.verify_ai_frameworks()
        self.verify_gpu_environment()
        
        # 输出总结
        print("\n" + "=" * 50)
        print("📊 验证结果总结")
        print(f"总检查项: {self.total_checks}")
        print(f"通过检查: {self.passed_checks}")
        print(f"失败检查: {self.total_checks - self.passed_checks}")
        
        success_rate = (self.passed_checks / self.total_checks) * 100 if self.total_checks > 0 else 0
        print(f"成功率: {success_rate:.1f}%")
        
        if success_rate >= 90:
            print("🎉 V4环境验证通过！企业级AI开发环境就绪！")
            return True
        elif success_rate >= 70:
            print("⚠️ V4环境基本可用，但有部分组件需要检查")
            return False
        else:
            print("❌ V4环境验证失败，需要重新构建")
            return False
    
    def generate_environment_report(self):
        """生成环境报告"""
        print("\n📋 V4环境配置报告")
        print("-" * 30)
        
        try:
            # Python版本
            import sys
            print(f"Python版本: {sys.version.split()[0]}")
            
            # PyTorch版本
            import torch
            print(f"PyTorch版本: {torch.__version__}")
            print(f"CUDA版本: {torch.version.cuda}")
            
            # JAX版本
            try:
                import jax
                print(f"JAX版本: {jax.__version__}")
            except:
                print("JAX版本: 未安装")
            
            # MLflow版本
            try:
                import mlflow
                print(f"MLflow版本: {mlflow.__version__}")
            except:
                print("MLflow版本: 未安装")
            
            # 系统信息
            import platform
            print(f"操作系统: {platform.system()} {platform.release()}")
            
        except Exception as e:
            print(f"生成报告时出错: {e}")

def main():
    """主函数"""
    verifier = V4EnvironmentVerifier()
    
    # 运行综合测试
    success = verifier.run_comprehensive_test()
    
    # 生成环境报告
    verifier.generate_environment_report()
    
    # 返回适当的退出码
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
